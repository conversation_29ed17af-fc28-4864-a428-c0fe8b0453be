# TradingView Clone - Installation Guide

This guide provides step-by-step instructions for installing and configuring the TradingView Clone module in Odoo 17.

## Prerequisites

### System Requirements
- **Operating System**: Linux (Ubuntu 20.04+ recommended), macOS, or Windows
- **Odoo Version**: 17.0 or higher
- **Python**: 3.8 or higher
- **Database**: PostgreSQL 12+ (recommended) or MySQL 8.0+
- **Memory**: Minimum 4GB RAM (8GB+ recommended for production)
- **Storage**: 10GB+ available disk space

### Required Software
- Odoo 17 Community or Enterprise Edition
- PostgreSQL or MySQL database server
- Python package manager (pip)
- Git (for cloning repository)

## Installation Steps

### 1. Prepare Odoo Environment

Ensure Odoo 17 is properly installed and running:

```bash
# Check Odoo version
odoo --version

# Verify Odoo is running
sudo systemctl status odoo
```

### 2. Download the Module

#### Option A: Clone from Repository
```bash
cd /path/to/odoo/addons
git clone <repository-url> tradingview_clone
```

#### Option B: Download and Extract
```bash
cd /path/to/odoo/addons
wget <download-url>/tradingview_clone.zip
unzip tradingview_clone.zip
```

### 3. Install Python Dependencies

```bash
# Navigate to module directory
cd tradingview_clone

# Install required packages
pip install -r requirements.txt

# For production environments, use virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 4. Configure Odoo

#### Update Odoo Configuration
Add the module path to your Odoo configuration file (`/etc/odoo/odoo.conf`):

```ini
[options]
addons_path = /path/to/odoo/addons,/path/to/tradingview_clone
```

#### Restart Odoo Service
```bash
sudo systemctl restart odoo
```

### 5. Install the Module

#### Via Odoo Web Interface
1. Log in to Odoo as Administrator
2. Go to **Apps** menu
3. Click **Update Apps List**
4. Search for "TradingView Clone"
5. Click **Install**

#### Via Command Line
```bash
odoo -d your_database -i tradingview_clone --stop-after-init
```

### 6. Initial Configuration

#### Set Up API Keys
1. Go to **Settings → Technical → Parameters → System Parameters**
2. Add the following parameters:

```
Key: tradingview.twelvedata_api_key
Value: your_twelvedata_api_key

Key: tradingview.newsapi_key  
Value: your_newsapi_key

Key: tradingview.binance_api_key (optional)
Value: your_binance_api_key

Key: tradingview.binance_secret_key (optional)
Value: your_binance_secret_key
```

#### Configure User Groups
1. Go to **Settings → Users & Companies → Groups**
2. Assign users to appropriate groups:
   - **TradingView User**: Basic access to market data
   - **TradingView Manager**: Full access including admin features

### 7. Initial Data Synchronization

#### Manual Sync via Wizard
1. Go to **Market → Manual Sync**
2. Configure sync options:
   - ✅ Sync Symbols
   - ✅ Sync OHLC Data
   - ✅ Sync News
   - ✅ Sync Events (optional)
3. Select symbol types (recommend "All Types")
4. Click **Start Synchronization**

#### Verify Sync Results
1. Check **Market → Sync Logs** for status
2. Verify data in **Market → Symbols**
3. Test market explorer at `/market`

## API Key Setup

### TwelveData API
1. Sign up at [TwelveData](https://twelvedata.com/)
2. Get your free API key (500 requests/day)
3. For production, consider paid plans

### NewsAPI
1. Register at [NewsAPI](https://newsapi.org/)
2. Get your free API key (1000 requests/day)
3. For production, consider paid plans

### Binance API (Optional)
1. Create account at [Binance](https://binance.com/)
2. Generate API key in account settings
3. Enable spot trading permissions
4. Keep secret key secure

## Verification

### Test Basic Functionality
1. Visit `/market` - should show symbol list
2. Click any symbol - should show detail page
3. Check charts load properly
4. Test search functionality

### Test User Features
1. Create test user account
2. Add symbols to watchlist
3. Set price alerts
4. Check portfolio page

### Test Admin Features
1. Access backend menus
2. Run manual sync
3. Check sync logs
4. Verify cron jobs are active

## Troubleshooting

### Common Issues

#### Module Not Appearing in Apps List
```bash
# Update apps list
odoo -d your_database --update-apps-list --stop-after-init

# Check module path in configuration
grep addons_path /etc/odoo/odoo.conf
```

#### Sync Failures
```bash
# Check API keys
Settings → Technical → System Parameters

# Check sync logs
Market → Sync Logs → Filter by "failure"

# Verify internet connectivity
curl -I https://api.twelvedata.com
```

#### Charts Not Loading
```bash
# Check browser console for errors
# Verify TradingView Lightweight Charts library
# Check symbol has OHLC data
```

#### Performance Issues
```bash
# Check database performance
# Monitor server resources
# Review cron job frequency
# Consider adding database indexes
```

### Log Files
Check Odoo log files for detailed error information:
```bash
# Default log location
tail -f /var/log/odoo/odoo-server.log

# Custom log location (check odoo.conf)
tail -f /path/to/your/odoo.log
```

## Production Deployment

### Security Considerations
1. Use HTTPS for all connections
2. Secure API keys in environment variables
3. Enable rate limiting
4. Regular security updates

### Performance Optimization
1. Configure database connection pooling
2. Enable Odoo caching
3. Use CDN for static assets
4. Monitor resource usage

### Backup Strategy
1. Regular database backups
2. Backup API configurations
3. Document custom modifications
4. Test restore procedures

## Maintenance

### Regular Tasks
1. Monitor sync logs daily
2. Update API keys before expiration
3. Clean old data periodically
4. Update module when new versions available

### Monitoring
1. Set up alerts for sync failures
2. Monitor API rate limits
3. Track system performance
4. Review user activity logs

## Support

### Getting Help
1. Check this documentation first
2. Review troubleshooting section
3. Check Odoo community forums
4. Contact system administrator

### Reporting Issues
When reporting issues, include:
1. Odoo version and module version
2. Error messages from logs
3. Steps to reproduce
4. System configuration details

---

**Installation complete! Your TradingView Clone is ready to use.**
