# -*- coding: utf-8 -*-

from odoo import http, fields, _
from odoo.http import request
from odoo.exceptions import UserError
import json
import logging

_logger = logging.getLogger(__name__)


class TradingViewMain(http.Controller):

    @http.route(['/market', '/market/page/<int:page>'], type='http', auth='public', website=True)
    def market_explorer(self, page=1, search='', symbol_type='', exchange='', region='', 
                       sector='', sort_by='symbol', sort_order='asc', **kwargs):
        """Market Explorer - Symbol listing with search and filters"""
        
        # Pagination settings
        symbols_per_page = 50
        offset = (page - 1) * symbols_per_page
        
        # Build domain for search and filters
        domain = [('active', '=', True)]
        
        if search:
            domain.extend([
                '|', '|',
                ('symbol', 'ilike', search),
                ('name', 'ilike', search),
                ('sector', 'ilike', search)
            ])
        
        if symbol_type:
            domain.append(('type', '=', symbol_type))
        
        if exchange:
            domain.append(('exchange', 'ilike', exchange))
        
        if region:
            domain.append(('region', 'ilike', region))
        
        if sector:
            domain.append(('sector', 'ilike', sector))
        
        # Sorting
        valid_sort_fields = ['symbol', 'name', 'current_price', 'daily_change_percent', 'volume', 'market_cap']
        if sort_by not in valid_sort_fields:
            sort_by = 'symbol'
        
        order = f"{sort_by} {'desc' if sort_order == 'desc' else 'asc'}"
        
        # Get symbols
        Symbol = request.env['tradingview.symbol']
        total_symbols = Symbol.search_count(domain)
        symbols = Symbol.search(domain, order=order, limit=symbols_per_page, offset=offset)
        
        # Pagination info
        total_pages = (total_symbols + symbols_per_page - 1) // symbols_per_page
        
        # Get filter options
        filter_options = self._get_filter_options()
        
        # Market status
        market_status = self._get_market_status()
        
        values = {
            'symbols': symbols,
            'total_symbols': total_symbols,
            'page': page,
            'total_pages': total_pages,
            'symbols_per_page': symbols_per_page,
            'search': search,
            'symbol_type': symbol_type,
            'exchange': exchange,
            'region': region,
            'sector': sector,
            'sort_by': sort_by,
            'sort_order': sort_order,
            'filter_options': filter_options,
            'market_status': market_status,
            'page_name': 'market_explorer',
        }
        
        return request.render('tradingview_clone.market_explorer_template', values)
    
    @http.route(['/market/<string:symbol_slug>'], type='http', auth='public', website=True)
    def symbol_detail(self, symbol_slug, **kwargs):
        """Symbol Detail Page - Dynamic page for any symbol"""
        
        # Find symbol by slug or symbol
        Symbol = request.env['tradingview.symbol']
        symbol = Symbol.search([
            '|',
            ('slug', '=', symbol_slug.lower()),
            ('symbol', '=', symbol_slug.upper())
        ], limit=1)
        
        if not symbol:
            # Try to find similar symbols
            similar_symbols = Symbol.search([
                ('symbol', 'ilike', symbol_slug)
            ], limit=5)
            
            return request.render('tradingview_clone.symbol_not_found_template', {
                'symbol_slug': symbol_slug,
                'similar_symbols': similar_symbols,
                'page_name': 'symbol_not_found',
            })
        
        # Update view count if user is logged in
        if request.env.user and not request.env.user._is_public():
            watchlist_item = request.env['tradingview.watchlist'].search([
                ('user_id', '=', request.env.user.id),
                ('symbol_id', '=', symbol.id)
            ], limit=1)
            if watchlist_item:
                watchlist_item.update_last_viewed()
        
        # Get related data
        latest_ohlc = symbol.get_latest_ohlc(limit=100)
        latest_news = symbol.get_latest_news(limit=10)
        upcoming_events = symbol.get_upcoming_events(limit=5)
        
        # Get technical indicators
        technical_indicators = self._get_technical_indicators(symbol)
        
        # Get chart data
        chart_data = self._get_chart_data(symbol)
        
        # Check if symbol is in user's watchlist
        in_watchlist = False
        if request.env.user and not request.env.user._is_public():
            in_watchlist = bool(request.env['tradingview.watchlist'].search([
                ('user_id', '=', request.env.user.id),
                ('symbol_id', '=', symbol.id)
            ], limit=1))
        
        # Get similar symbols
        similar_symbols = self._get_similar_symbols(symbol)
        
        # Market status
        market_status = self._get_market_status()
        
        values = {
            'symbol': symbol,
            'latest_ohlc': latest_ohlc,
            'latest_news': latest_news,
            'upcoming_events': upcoming_events,
            'technical_indicators': technical_indicators,
            'chart_data': chart_data,
            'in_watchlist': in_watchlist,
            'similar_symbols': similar_symbols,
            'market_status': market_status,
            'page_name': 'symbol_detail',
        }
        
        return request.render('tradingview_clone.symbol_detail_template', values)
    
    @http.route('/market/search', type='json', auth='public', methods=['POST'])
    def symbol_search(self, query='', limit=10):
        """AJAX symbol search for autocomplete"""
        
        if not query or len(query) < 2:
            return {'symbols': []}
        
        domain = [
            ('active', '=', True),
            '|', '|',
            ('symbol', 'ilike', query),
            ('name', 'ilike', query),
            ('slug', 'ilike', query)
        ]
        
        symbols = request.env['tradingview.symbol'].search(domain, limit=limit)
        
        results = []
        for symbol in symbols:
            results.append({
                'symbol': symbol.symbol,
                'name': symbol.name,
                'slug': symbol.slug,
                'type': symbol.type,
                'exchange': symbol.exchange,
                'current_price': symbol.current_price,
                'daily_change_percent': symbol.daily_change_percent,
                'url': f'/market/{symbol.slug}'
            })
        
        return {'symbols': results}
    
    @http.route('/market/<string:symbol_slug>/chart-data', type='json', auth='public')
    def get_chart_data(self, symbol_slug, timeframe='1d', limit=100):
        """Get chart data for a symbol"""
        
        symbol = request.env['tradingview.symbol'].search([
            '|',
            ('slug', '=', symbol_slug.lower()),
            ('symbol', '=', symbol_slug.upper())
        ], limit=1)
        
        if not symbol:
            return {'error': 'Symbol not found'}
        
        chart_data = request.env['tradingview.ohlc'].get_chart_data(
            symbol.id, timeframe, limit
        )
        
        return {
            'symbol': symbol.symbol,
            'timeframe': timeframe,
            'data': chart_data
        }
    
    @http.route('/market/<string:symbol_slug>/add-to-watchlist', type='json', auth='user', methods=['POST'])
    def add_to_watchlist(self, symbol_slug, category='stocks'):
        """Add symbol to user's watchlist"""
        
        symbol = request.env['tradingview.symbol'].search([
            '|',
            ('slug', '=', symbol_slug.lower()),
            ('symbol', '=', symbol_slug.upper())
        ], limit=1)
        
        if not symbol:
            return {'error': 'Symbol not found'}
        
        try:
            result = request.env['tradingview.watchlist'].add_to_watchlist(
                symbol.id, request.env.user.id, category
            )
            return {
                'success': True,
                'message': result['message']
            }
        except UserError as e:
            return {'error': str(e)}
    
    @http.route('/market/<string:symbol_slug>/remove-from-watchlist', type='json', auth='user', methods=['POST'])
    def remove_from_watchlist(self, symbol_slug):
        """Remove symbol from user's watchlist"""
        
        symbol = request.env['tradingview.symbol'].search([
            '|',
            ('slug', '=', symbol_slug.lower()),
            ('symbol', '=', symbol_slug.upper())
        ], limit=1)
        
        if not symbol:
            return {'error': 'Symbol not found'}
        
        watchlist_item = request.env['tradingview.watchlist'].search([
            ('user_id', '=', request.env.user.id),
            ('symbol_id', '=', symbol.id)
        ], limit=1)
        
        if watchlist_item:
            watchlist_item.unlink()
            return {
                'success': True,
                'message': f'{symbol.symbol} removed from your watchlist'
            }
        else:
            return {'error': 'Symbol not in watchlist'}
    
    def _get_filter_options(self):
        """Get available filter options for market explorer"""
        Symbol = request.env['tradingview.symbol']
        
        # Get unique values for filters
        types = Symbol.read_group(
            [('active', '=', True)], ['type'], ['type']
        )
        
        exchanges = Symbol.search([('active', '=', True)]).mapped('exchange')
        exchanges = list(set([e for e in exchanges if e]))
        
        regions = Symbol.search([('active', '=', True)]).mapped('region')
        regions = list(set([r for r in regions if r]))
        
        sectors = Symbol.search([('active', '=', True)]).mapped('sector')
        sectors = list(set([s for s in sectors if s]))
        
        return {
            'types': [{'key': t['type'], 'count': t['type_count']} for t in types],
            'exchanges': sorted(exchanges),
            'regions': sorted(regions),
            'sectors': sorted(sectors),
        }
    
    def _get_technical_indicators(self, symbol):
        """Get latest technical indicators for a symbol"""
        indicators = request.env['tradingview.technical'].get_latest_indicators(
            symbol.id, 
            timeframe='1d',
            indicators=['rsi', 'sma_20', 'sma_50', 'sma_200', 'macd_line', 'macd_signal']
        )
        return indicators
    
    def _get_chart_data(self, symbol):
        """Get chart data for symbol detail page"""
        return request.env['tradingview.ohlc'].get_chart_data(
            symbol.id, timeframe='1d', limit=100
        )
    
    def _get_similar_symbols(self, symbol, limit=5):
        """Get similar symbols based on sector/industry"""
        domain = [
            ('active', '=', True),
            ('id', '!=', symbol.id)
        ]
        
        if symbol.sector:
            domain.append(('sector', '=', symbol.sector))
        elif symbol.type:
            domain.append(('type', '=', symbol.type))
        
        return request.env['tradingview.symbol'].search(domain, limit=limit)
    
    def _get_market_status(self):
        """Get current market status"""
        try:
            is_open = request.env['ir.config_parameter'].sudo().get_param(
                'tradingview.market_open', 'false'
            ) == 'true'
            
            last_update = request.env['ir.config_parameter'].sudo().get_param(
                'tradingview.last_market_update', ''
            )
            
            return {
                'is_open': is_open,
                'status': 'Open' if is_open else 'Closed',
                'last_update': last_update
            }
        except:
            return {
                'is_open': False,
                'status': 'Unknown',
                'last_update': ''
            }
