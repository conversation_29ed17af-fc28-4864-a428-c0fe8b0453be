# -*- coding: utf-8 -*-

from odoo import http, fields, _
from odoo.http import request
import json
import logging
from datetime import datetime, timedelta

_logger = logging.getLogger(__name__)


class TradingViewMarketController(http.Controller):

    @http.route('/market/api/symbols', type='json', auth='public')
    def get_symbols(self, symbol_type=None, exchange=None, limit=50, offset=0):
        """API endpoint to get symbols with filtering"""
        
        domain = [('active', '=', True)]
        
        if symbol_type:
            domain.append(('type', '=', symbol_type))
        
        if exchange:
            domain.append(('exchange', 'ilike', exchange))
        
        symbols = request.env['tradingview.symbol'].search(
            domain, limit=limit, offset=offset, order='symbol asc'
        )
        
        result = []
        for symbol in symbols:
            result.append({
                'symbol': symbol.symbol,
                'name': symbol.name,
                'slug': symbol.slug,
                'type': symbol.type,
                'exchange': symbol.exchange,
                'region': symbol.region,
                'currency': symbol.currency,
                'current_price': symbol.current_price,
                'daily_change': symbol.daily_change,
                'daily_change_percent': symbol.daily_change_percent,
                'volume': symbol.volume,
                'market_cap': symbol.market_cap,
                'last_updated': symbol.last_updated.isoformat() if symbol.last_updated else None,
            })
        
        return {
            'symbols': result,
            'total': request.env['tradingview.symbol'].search_count(domain),
            'limit': limit,
            'offset': offset
        }
    
    @http.route('/market/api/symbol/<string:symbol_slug>', type='json', auth='public')
    def get_symbol_detail(self, symbol_slug):
        """API endpoint to get detailed symbol information"""
        
        symbol = request.env['tradingview.symbol'].search([
            '|',
            ('slug', '=', symbol_slug.lower()),
            ('symbol', '=', symbol_slug.upper())
        ], limit=1)
        
        if not symbol:
            return {'error': 'Symbol not found'}
        
        # Get latest OHLC data
        latest_ohlc = symbol.get_latest_ohlc(limit=1)
        ohlc_data = None
        if latest_ohlc:
            ohlc = latest_ohlc[0]
            ohlc_data = {
                'timestamp': ohlc.timestamp.isoformat(),
                'open': ohlc.open,
                'high': ohlc.high,
                'low': ohlc.low,
                'close': ohlc.close,
                'volume': ohlc.volume,
                'timeframe': ohlc.timeframe
            }
        
        # Get technical indicators
        technical_indicators = request.env['tradingview.technical'].get_latest_indicators(
            symbol.id, timeframe='1d'
        )
        
        return {
            'symbol': {
                'symbol': symbol.symbol,
                'name': symbol.name,
                'slug': symbol.slug,
                'type': symbol.type,
                'exchange': symbol.exchange,
                'region': symbol.region,
                'currency': symbol.currency,
                'sector': symbol.sector,
                'industry': symbol.industry,
                'description': symbol.description,
                'website': symbol.website,
                'current_price': symbol.current_price,
                'daily_change': symbol.daily_change,
                'daily_change_percent': symbol.daily_change_percent,
                'volume': symbol.volume,
                'market_cap': symbol.market_cap,
                'day_high': symbol.day_high,
                'day_low': symbol.day_low,
                'week_52_high': symbol.week_52_high,
                'week_52_low': symbol.week_52_low,
                'last_updated': symbol.last_updated.isoformat() if symbol.last_updated else None,
            },
            'latest_ohlc': ohlc_data,
            'technical_indicators': technical_indicators
        }
    
    @http.route('/market/api/ohlc/<string:symbol_slug>', type='json', auth='public')
    def get_ohlc_data(self, symbol_slug, timeframe='1d', limit=100, start_date=None, end_date=None):
        """API endpoint to get OHLC data for a symbol"""
        
        symbol = request.env['tradingview.symbol'].search([
            '|',
            ('slug', '=', symbol_slug.lower()),
            ('symbol', '=', symbol_slug.upper())
        ], limit=1)
        
        if not symbol:
            return {'error': 'Symbol not found'}
        
        domain = [
            ('symbol_id', '=', symbol.id),
            ('timeframe', '=', timeframe)
        ]
        
        if start_date:
            try:
                start_dt = datetime.fromisoformat(start_date)
                domain.append(('timestamp', '>=', start_dt))
            except ValueError:
                return {'error': 'Invalid start_date format. Use ISO format.'}
        
        if end_date:
            try:
                end_dt = datetime.fromisoformat(end_date)
                domain.append(('timestamp', '<=', end_dt))
            except ValueError:
                return {'error': 'Invalid end_date format. Use ISO format.'}
        
        ohlc_records = request.env['tradingview.ohlc'].search(
            domain, order='timestamp asc', limit=limit
        )
        
        data = []
        for record in ohlc_records:
            data.append({
                'timestamp': record.timestamp.isoformat(),
                'open': record.open,
                'high': record.high,
                'low': record.low,
                'close': record.close,
                'volume': record.volume,
                'price_change': record.price_change,
                'price_change_percent': record.price_change_percent,
            })
        
        return {
            'symbol': symbol.symbol,
            'timeframe': timeframe,
            'data': data,
            'count': len(data)
        }
    
    @http.route('/market/api/news/<string:symbol_slug>', type='json', auth='public')
    def get_symbol_news(self, symbol_slug, limit=10, category=None):
        """API endpoint to get news for a symbol"""
        
        symbol = request.env['tradingview.symbol'].search([
            '|',
            ('slug', '=', symbol_slug.lower()),
            ('symbol', '=', symbol_slug.upper())
        ], limit=1)
        
        if not symbol:
            return {'error': 'Symbol not found'}
        
        domain = [
            ('symbol_id', '=', symbol.id),
            ('active', '=', True)
        ]
        
        if category:
            domain.append(('category', '=', category))
        
        news_records = request.env['tradingview.news'].search(
            domain, order='published_at desc', limit=limit
        )
        
        news_data = []
        for news in news_records:
            news_data.append({
                'title': news.title,
                'summary': news.display_summary,
                'link': news.link,
                'source': news.source,
                'author': news.author,
                'published_at': news.published_at.isoformat(),
                'category': news.category,
                'sentiment': news.sentiment,
                'impact_level': news.impact_level,
                'is_breaking': news.is_breaking,
                'is_featured': news.is_featured,
            })
        
        return {
            'symbol': symbol.symbol,
            'news': news_data,
            'count': len(news_data)
        }
    
    @http.route('/market/api/events/<string:symbol_slug>', type='json', auth='public')
    def get_symbol_events(self, symbol_slug, limit=10, upcoming_only=True):
        """API endpoint to get events for a symbol"""
        
        symbol = request.env['tradingview.symbol'].search([
            '|',
            ('slug', '=', symbol_slug.lower()),
            ('symbol', '=', symbol_slug.upper())
        ], limit=1)
        
        if not symbol:
            return {'error': 'Symbol not found'}
        
        domain = [('symbol_id', '=', symbol.id)]
        
        if upcoming_only:
            domain.append(('date', '>=', fields.Datetime.now()))
        
        events = request.env['tradingview.event'].search(
            domain, order='date asc', limit=limit
        )
        
        events_data = []
        for event in events:
            events_data.append({
                'title': event.title,
                'description': event.description,
                'date': event.date.isoformat(),
                'event_type': event.event_type,
                'impact_level': event.impact_level,
                'status': event.status,
                'location': event.location,
                'is_upcoming': event.is_upcoming,
                'time_until': event.time_until,
                'estimated_eps': event.estimated_eps,
                'actual_eps': event.actual_eps,
                'estimated_revenue': event.estimated_revenue,
                'actual_revenue': event.actual_revenue,
            })
        
        return {
            'symbol': symbol.symbol,
            'events': events_data,
            'count': len(events_data)
        }
    
    @http.route('/market/api/technical/<string:symbol_slug>', type='json', auth='public')
    def get_technical_indicators(self, symbol_slug, timeframe='1d', indicators=None):
        """API endpoint to get technical indicators for a symbol"""
        
        symbol = request.env['tradingview.symbol'].search([
            '|',
            ('slug', '=', symbol_slug.lower()),
            ('symbol', '=', symbol_slug.upper())
        ], limit=1)
        
        if not symbol:
            return {'error': 'Symbol not found'}
        
        if indicators:
            if isinstance(indicators, str):
                indicators = indicators.split(',')
        
        technical_data = request.env['tradingview.technical'].get_latest_indicators(
            symbol.id, timeframe=timeframe, indicators=indicators
        )
        
        return {
            'symbol': symbol.symbol,
            'timeframe': timeframe,
            'indicators': technical_data
        }
    
    @http.route('/market/api/watchlist', type='json', auth='user')
    def get_user_watchlist(self, category=None, limit=None):
        """API endpoint to get user's watchlist"""
        
        watchlist_items = request.env['tradingview.watchlist'].get_user_watchlist(
            user_id=request.env.user.id,
            category=category,
            limit=limit
        )
        
        watchlist_data = []
        for item in watchlist_items:
            summary = item.get_watchlist_summary()
            watchlist_data.append(summary)
        
        return {
            'watchlist': watchlist_data,
            'count': len(watchlist_data)
        }
    
    @http.route('/market/api/market-status', type='json', auth='public')
    def get_market_status(self):
        """API endpoint to get current market status"""
        
        try:
            is_open = request.env['ir.config_parameter'].sudo().get_param(
                'tradingview.market_open', 'false'
            ) == 'true'
            
            last_update = request.env['ir.config_parameter'].sudo().get_param(
                'tradingview.last_market_update', ''
            )
            
            # Get market statistics
            total_symbols = request.env['tradingview.symbol'].search_count([('active', '=', True)])
            
            # Get top gainers and losers
            top_gainers = request.env['tradingview.symbol'].search([
                ('active', '=', True),
                ('daily_change_percent', '>', 0)
            ], order='daily_change_percent desc', limit=5)
            
            top_losers = request.env['tradingview.symbol'].search([
                ('active', '=', True),
                ('daily_change_percent', '<', 0)
            ], order='daily_change_percent asc', limit=5)
            
            gainers_data = []
            for symbol in top_gainers:
                gainers_data.append({
                    'symbol': symbol.symbol,
                    'name': symbol.name,
                    'slug': symbol.slug,
                    'current_price': symbol.current_price,
                    'daily_change_percent': symbol.daily_change_percent,
                })
            
            losers_data = []
            for symbol in top_losers:
                losers_data.append({
                    'symbol': symbol.symbol,
                    'name': symbol.name,
                    'slug': symbol.slug,
                    'current_price': symbol.current_price,
                    'daily_change_percent': symbol.daily_change_percent,
                })
            
            return {
                'market_status': {
                    'is_open': is_open,
                    'status': 'Open' if is_open else 'Closed',
                    'last_update': last_update
                },
                'statistics': {
                    'total_symbols': total_symbols,
                    'top_gainers': gainers_data,
                    'top_losers': losers_data
                }
            }
            
        except Exception as e:
            _logger.error(f"Error getting market status: {e}")
            return {
                'error': 'Unable to fetch market status',
                'market_status': {
                    'is_open': False,
                    'status': 'Unknown',
                    'last_update': ''
                }
            }
    
    @http.route('/market/api/trending', type='json', auth='public')
    def get_trending_symbols(self, limit=10):
        """API endpoint to get trending symbols based on watchlist count"""
        
        trending_symbols = request.env['tradingview.symbol'].search([
            ('active', '=', True),
            ('watchlist_count', '>', 0)
        ], order='watchlist_count desc', limit=limit)
        
        trending_data = []
        for symbol in trending_symbols:
            trending_data.append({
                'symbol': symbol.symbol,
                'name': symbol.name,
                'slug': symbol.slug,
                'type': symbol.type,
                'current_price': symbol.current_price,
                'daily_change_percent': symbol.daily_change_percent,
                'watchlist_count': symbol.watchlist_count,
            })
        
        return {
            'trending_symbols': trending_data,
            'count': len(trending_data)
        }
