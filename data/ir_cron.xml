<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Daily Symbol List Sync -->
        <record id="cron_sync_symbols_daily" model="ir.cron">
            <field name="name">TradingView: Daily Symbol Sync</field>
            <field name="model_id" ref="model_tradingview_symbol"/>
            <field name="state">code</field>
            <field name="code">
                api_service = env['tradingview.api.service']
                try:
                    api_service.sync_symbols_twelvedata(['stock', 'forex', 'crypto'])
                except Exception as e:
                    env['tradingview.sync_log'].create({
                        'api_name': 'twelvedata',
                        'sync_type': 'symbols',
                        'status': 'failure',
                        'error_message': str(e),
                        'triggered_by': 'cron',
                        'start_time': fields.Datetime.now(),
                        'end_time': fields.Datetime.now(),
                    })
            </field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="active">False</field>
            <field name="user_id" ref="base.user_root"/>
        </record>
        
        <!-- 5-Minute OHLC Data Sync (Active Trading Hours) -->
        <record id="cron_sync_ohlc_5min" model="ir.cron">
            <field name="name">TradingView: 5-Minute OHLC Sync</field>
            <field name="model_id" ref="model_tradingview_ohlc"/>
            <field name="state">code</field>
            <field name="code">
                # Only run during active trading hours (9 AM - 4 PM EST, Mon-Fri)
                import datetime
                now = datetime.datetime.now()
                if now.weekday() &lt; 5 and 9 &lt;= now.hour &lt; 16:
                    api_service = env['tradingview.api.service']
                    try:
                        # Get top 20 most watched symbols
                        top_symbols = env['tradingview.symbol'].search([
                            ('active', '=', True),
                            ('type', 'in', ['stock', 'crypto'])
                        ], order='watchlist_count desc', limit=20)
                        
                        symbol_list = [s.symbol for s in top_symbols]
                        if symbol_list:
                            api_service.sync_crypto_data_binance(symbol_list[:10])  # Crypto symbols
                            # Add stock data sync here when implemented
                    except Exception as e:
                        env['tradingview.sync_log'].create({
                            'api_name': 'binance',
                            'sync_type': 'ohlc',
                            'status': 'failure',
                            'error_message': str(e),
                            'triggered_by': 'cron',
                            'start_time': fields.Datetime.now(),
                            'end_time': fields.Datetime.now(),
                        })
            </field>
            <field name="interval_number">5</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
            <field name="user_id" ref="base.user_root"/>
        </record>

        <!-- 15-Minute Technical Indicators Calculation -->
        <record id="cron_calculate_technical_indicators" model="ir.cron">
            <field name="name">TradingView: Calculate Technical Indicators</field>
            <field name="model_id" ref="model_tradingview_technical"/>
            <field name="state">code</field>
            <field name="code">
                # Calculate technical indicators for active symbols
                try:
                    symbols = env['tradingview.symbol'].search([
                        ('active', '=', True),
                        ('ohlc_count', '>', 20)  # Need sufficient data
                    ], limit=50)

                    for symbol in symbols:
                        # Calculate RSI
                        env['tradingview.technical']._calculate_rsi(symbol.id)
                        # Calculate Moving Averages
                        env['tradingview.technical']._calculate_moving_averages(symbol.id)
                        # Calculate MACD
                        env['tradingview.technical']._calculate_macd(symbol.id)

                except Exception as e:
                    env['tradingview.sync_log'].create({
                        'api_name': 'custom_api',
                        'sync_type': 'technical',
                        'status': 'failure',
                        'error_message': str(e),
                        'triggered_by': 'cron',
                        'start_time': fields.Datetime.now(),
                        'end_time': fields.Datetime.now(),
                    })
            </field>
            <field name="interval_number">15</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
            <field name="user_id" ref="base.user_root"/>
        </record>
        
        <!-- Hourly News Sync -->
        <record id="cron_sync_news_hourly" model="ir.cron">
            <field name="name">TradingView: Hourly News Sync</field>
            <field name="model_id" ref="model_tradingview_news"/>
            <field name="state">code</field>
            <field name="code">
                api_service = env['tradingview.api.service']
                try:
                    # Get top symbols for news sync
                    top_symbols = env['tradingview.symbol'].search([
                        ('active', '=', True),
                        ('type', 'in', ['stock', 'crypto'])
                    ], order='watchlist_count desc', limit=10)
                    
                    symbol_list = [s.symbol for s in top_symbols]
                    api_service.sync_financial_news(symbol_list, days_back=1)
                    
                except Exception as e:
                    env['tradingview.sync_log'].create({
                        'api_name': 'newsapi',
                        'sync_type': 'news',
                        'status': 'failure',
                        'error_message': str(e),
                        'triggered_by': 'cron',
                        'start_time': fields.Datetime.now(),
                        'end_time': fields.Datetime.now(),
                    })
            </field>
            <field name="interval_number">1</field>
            <field name="interval_type">hours</field>
            <field name="numbercall">-1</field>
            <field name="active">False</field>
            <field name="user_id" ref="base.user_root"/>
        </record>
        
        <!-- Daily Financial Events Sync -->
        <record id="cron_sync_events_daily" model="ir.cron">
            <field name="name">TradingView: Daily Events Sync</field>
            <field name="model_id" ref="model_tradingview_event"/>
            <field name="state">code</field>
            <field name="code">
                try:
                    # Sync upcoming earnings events
                    env['tradingview.event']._sync_earnings_calendar()
                    
                    # Sync economic events
                    env['tradingview.event']._sync_economic_events()
                    
                    # Update event statuses
                    env['tradingview.event']._update_event_statuses()
                    
                except Exception as e:
                    env['tradingview.sync_log'].create({
                        'api_name': 'financial_modeling_prep',
                        'sync_type': 'events',
                        'status': 'failure',
                        'error_message': str(e),
                        'triggered_by': 'cron',
                        'start_time': fields.Datetime.now(),
                        'end_time': fields.Datetime.now(),
                    })
            </field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="active">False</field>
            <field name="user_id" ref="base.user_root"/>
        </record>
        
        <!-- Price Alert Check (Every 5 Minutes) -->
        <record id="cron_check_price_alerts" model="ir.cron">
            <field name="name">TradingView: Check Price Alerts</field>
            <field name="model_id" ref="model_tradingview_watchlist"/>
            <field name="state">code</field>
            <field name="code">
                try:
                    # Check price alerts for all users
                    alerts_triggered = env['tradingview.watchlist'].check_price_alerts()
                    
                    # Log the number of alerts triggered
                    if alerts_triggered:
                        env['tradingview.sync_log'].create({
                            'api_name': 'custom_api',
                            'sync_type': 'real_time',
                            'status': 'success',
                            'records_processed': len(alerts_triggered),
                            'triggered_by': 'cron',
                            'start_time': fields.Datetime.now(),
                            'end_time': fields.Datetime.now(),
                        })
                        
                except Exception as e:
                    env['tradingview.sync_log'].create({
                        'api_name': 'custom_api',
                        'sync_type': 'real_time',
                        'status': 'failure',
                        'error_message': str(e),
                        'triggered_by': 'cron',
                        'start_time': fields.Datetime.now(),
                        'end_time': fields.Datetime.now(),
                    })
            </field>
            <field name="interval_number">5</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="active">False</field>
            <field name="user_id" ref="base.user_root"/>
        </record>
        
        <!-- Weekly Data Cleanup -->
        <record id="cron_cleanup_old_data" model="ir.cron">
            <field name="name">TradingView: Weekly Data Cleanup</field>
            <field name="model_id" ref="model_tradingview_ohlc"/>
            <field name="state">code</field>
            <field name="code">
                try:
                    # Clean up old OHLC data (keep 1 year)
                    cleaned_ohlc = env['tradingview.ohlc'].cleanup_old_data(days_to_keep=365)
                    
                    # Clean up old sync logs (keep 30 days)
                    cleaned_logs = env['tradingview.sync_log'].cleanup_old_logs(days_to_keep=30)
                    
                    # Log cleanup results
                    env['tradingview.sync_log'].create({
                        'api_name': 'custom_api',
                        'sync_type': 'full_sync',
                        'status': 'success',
                        'records_processed': cleaned_ohlc + cleaned_logs,
                        'triggered_by': 'cron',
                        'start_time': fields.Datetime.now(),
                        'end_time': fields.Datetime.now(),
                        'parameters': f"Cleaned {cleaned_ohlc} OHLC records and {cleaned_logs} sync logs"
                    })
                    
                except Exception as e:
                    env['tradingview.sync_log'].create({
                        'api_name': 'custom_api',
                        'sync_type': 'full_sync',
                        'status': 'failure',
                        'error_message': str(e),
                        'triggered_by': 'cron',
                        'start_time': fields.Datetime.now(),
                        'end_time': fields.Datetime.now(),
                    })
            </field>
            <field name="interval_number">1</field>
            <field name="interval_type">weeks</field>
            <field name="numbercall">-1</field>
            <field name="active">False</field>
            <field name="user_id" ref="base.user_root"/>
        </record>
        
        <!-- Market Hours Status Update -->
        <record id="cron_update_market_status" model="ir.cron">
            <field name="name">TradingView: Update Market Status</field>
            <field name="model_id" ref="model_tradingview_symbol"/>
            <field name="state">code</field>
            <field name="code">
                try:
                    # Update market status and trading hours
                    import datetime
                    now = datetime.datetime.now()
                    
                    # US Market Hours (9:30 AM - 4:00 PM EST, Mon-Fri)
                    is_market_open = (
                        now.weekday() &lt; 5 and  # Monday to Friday
                        9.5 &lt;= now.hour + now.minute/60 &lt; 16  # 9:30 AM to 4:00 PM
                    )
                    
                    # Update system parameter
                    env['ir.config_parameter'].sudo().set_param(
                        'tradingview.market_open', 
                        'true' if is_market_open else 'false'
                    )
                    env['ir.config_parameter'].sudo().set_param(
                        'tradingview.last_market_update', 
                        now.isoformat()
                    )
                    
                except Exception as e:
                    pass  # Silent fail for this utility cron
            </field>
            <field name="interval_number">1</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
            <field name="user_id" ref="base.user_root"/>
        </record>
        
    </data>
</odoo>
