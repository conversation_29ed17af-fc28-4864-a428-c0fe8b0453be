<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- API Configuration Parameters -->
        <record id="param_twelvedata_api_key" model="ir.config_parameter">
            <field name="key">tradingview.twelvedata_api_key</field>
            <field name="value"></field>
        </record>
        
        <record id="param_newsapi_key" model="ir.config_parameter">
            <field name="key">tradingview.newsapi_key</field>
            <field name="value"></field>
        </record>
        
        <record id="param_financial_modeling_prep_key" model="ir.config_parameter">
            <field name="key">tradingview.financial_modeling_prep_key</field>
            <field name="value"></field>
        </record>
        
        <record id="param_alpha_vantage_key" model="ir.config_parameter">
            <field name="key">tradingview.alpha_vantage_key</field>
            <field name="value"></field>
        </record>
        
        <record id="param_polygon_key" model="ir.config_parameter">
            <field name="key">tradingview.polygon_key</field>
            <field name="value"></field>
        </record>
        
        <record id="param_iex_cloud_key" model="ir.config_parameter">
            <field name="key">tradingview.iex_cloud_key</field>
            <field name="value"></field>
        </record>
        
        <!-- Market Configuration -->
        <record id="param_market_open" model="ir.config_parameter">
            <field name="key">tradingview.market_open</field>
            <field name="value">false</field>
        </record>
        
        <record id="param_last_market_update" model="ir.config_parameter">
            <field name="key">tradingview.last_market_update</field>
            <field name="value"></field>
        </record>
        
        <!-- Data Sync Configuration -->
        <record id="param_sync_enabled" model="ir.config_parameter">
            <field name="key">tradingview.sync_enabled</field>
            <field name="value">true</field>
        </record>
        
        <record id="param_max_symbols_per_sync" model="ir.config_parameter">
            <field name="key">tradingview.max_symbols_per_sync</field>
            <field name="value">100</field>
        </record>
        
        <record id="param_ohlc_retention_days" model="ir.config_parameter">
            <field name="key">tradingview.ohlc_retention_days</field>
            <field name="value">365</field>
        </record>
        
        <record id="param_news_retention_days" model="ir.config_parameter">
            <field name="key">tradingview.news_retention_days</field>
            <field name="value">90</field>
        </record>
        
        <record id="param_sync_log_retention_days" model="ir.config_parameter">
            <field name="key">tradingview.sync_log_retention_days</field>
            <field name="value">30</field>
        </record>
        
        <!-- Rate Limiting Configuration -->
        <record id="param_api_rate_limit_per_minute" model="ir.config_parameter">
            <field name="key">tradingview.api_rate_limit_per_minute</field>
            <field name="value">60</field>
        </record>
        
        <record id="param_api_timeout_seconds" model="ir.config_parameter">
            <field name="key">tradingview.api_timeout_seconds</field>
            <field name="value">30</field>
        </record>
        
        <!-- Chart Configuration -->
        <record id="param_default_chart_timeframe" model="ir.config_parameter">
            <field name="key">tradingview.default_chart_timeframe</field>
            <field name="value">1d</field>
        </record>
        
        <record id="param_max_chart_data_points" model="ir.config_parameter">
            <field name="key">tradingview.max_chart_data_points</field>
            <field name="value">1000</field>
        </record>
        
        <!-- Notification Configuration -->
        <record id="param_enable_price_alerts" model="ir.config_parameter">
            <field name="key">tradingview.enable_price_alerts</field>
            <field name="value">true</field>
        </record>
        
        <record id="param_enable_news_notifications" model="ir.config_parameter">
            <field name="key">tradingview.enable_news_notifications</field>
            <field name="value">true</field>
        </record>
        
        <record id="param_enable_event_notifications" model="ir.config_parameter">
            <field name="key">tradingview.enable_event_notifications</field>
            <field name="value">true</field>
        </record>
        
        <!-- Performance Configuration -->
        <record id="param_enable_caching" model="ir.config_parameter">
            <field name="key">tradingview.enable_caching</field>
            <field name="value">true</field>
        </record>
        
        <record id="param_cache_timeout_seconds" model="ir.config_parameter">
            <field name="key">tradingview.cache_timeout_seconds</field>
            <field name="value">300</field>
        </record>
        
        <!-- Debug Configuration -->
        <record id="param_debug_mode" model="ir.config_parameter">
            <field name="key">tradingview.debug_mode</field>
            <field name="value">false</field>
        </record>
        
        <record id="param_log_api_requests" model="ir.config_parameter">
            <field name="key">tradingview.log_api_requests</field>
            <field name="value">false</field>
        </record>
        
    </data>
</odoo>
