<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Demo Symbols -->
        <record id="symbol_aapl" model="tradingview.symbol">
            <field name="symbol">AAPL</field>
            <field name="name">Apple Inc.</field>
            <field name="slug">aapl</field>
            <field name="type">stock</field>
            <field name="exchange">NASDAQ</field>
            <field name="region">US</field>
            <field name="currency">USD</field>
            <field name="sector">Technology</field>
            <field name="industry">Consumer Electronics</field>
            <field name="description">Apple Inc. designs, manufactures, and markets smartphones, personal computers, tablets, wearables, and accessories worldwide.</field>
            <field name="website">https://www.apple.com</field>
            <field name="current_price">175.50</field>
            <field name="daily_change">2.30</field>
            <field name="daily_change_percent">1.33</field>
            <field name="volume">45000000</field>
            <field name="market_cap">***********00</field>
            <field name="week_52_high">198.23</field>
            <field name="week_52_low">124.17</field>
            <field name="day_high">176.80</field>
            <field name="day_low">173.20</field>
        </record>
        
        <record id="symbol_btcusd" model="tradingview.symbol">
            <field name="symbol">BTCUSD</field>
            <field name="name">Bitcoin USD</field>
            <field name="slug">btcusd</field>
            <field name="type">crypto</field>
            <field name="exchange">Binance</field>
            <field name="region">Global</field>
            <field name="currency">USD</field>
            <field name="description">Bitcoin is a decentralized digital currency, without a central bank or single administrator.</field>
            <field name="current_price">43250.00</field>
            <field name="daily_change">1250.00</field>
            <field name="daily_change_percent">2.98</field>
            <field name="volume">***********</field>
            <field name="week_52_high">69000.00</field>
            <field name="week_52_low">15500.00</field>
            <field name="day_high">43800.00</field>
            <field name="day_low">42100.00</field>
        </record>
        
        <record id="symbol_eurusd" model="tradingview.symbol">
            <field name="symbol">EURUSD</field>
            <field name="name">Euro US Dollar</field>
            <field name="slug">eurusd</field>
            <field name="type">forex</field>
            <field name="exchange">Forex</field>
            <field name="region">Global</field>
            <field name="currency">USD</field>
            <field name="description">EUR/USD is the most traded currency pair in the forex market.</field>
            <field name="current_price">1.0875</field>
            <field name="daily_change">0.0023</field>
            <field name="daily_change_percent">0.21</field>
            <field name="volume">1500000000</field>
            <field name="week_52_high">1.1275</field>
            <field name="week_52_low">0.9535</field>
            <field name="day_high">1.0890</field>
            <field name="day_low">1.0845</field>
        </record>
        
        <record id="symbol_tsla" model="tradingview.symbol">
            <field name="symbol">TSLA</field>
            <field name="name">Tesla Inc.</field>
            <field name="slug">tsla</field>
            <field name="type">stock</field>
            <field name="exchange">NASDAQ</field>
            <field name="region">US</field>
            <field name="currency">USD</field>
            <field name="sector">Consumer Cyclical</field>
            <field name="industry">Auto Manufacturers</field>
            <field name="description">Tesla, Inc. designs, develops, manufactures, leases, and sells electric vehicles, and energy generation and storage systems.</field>
            <field name="website">https://www.tesla.com</field>
            <field name="current_price">248.50</field>
            <field name="daily_change">-5.20</field>
            <field name="daily_change_percent">-2.05</field>
            <field name="volume">32000000</field>
            <field name="market_cap">790000000000</field>
            <field name="week_52_high">414.50</field>
            <field name="week_52_low">101.81</field>
            <field name="day_high">252.00</field>
            <field name="day_low">245.30</field>
        </record>
        
        <record id="symbol_ethusd" model="tradingview.symbol">
            <field name="symbol">ETHUSD</field>
            <field name="name">Ethereum USD</field>
            <field name="slug">ethusd</field>
            <field name="type">crypto</field>
            <field name="exchange">Binance</field>
            <field name="region">Global</field>
            <field name="currency">USD</field>
            <field name="description">Ethereum is a decentralized platform that runs smart contracts.</field>
            <field name="current_price">2650.00</field>
            <field name="daily_change">85.00</field>
            <field name="daily_change_percent">3.32</field>
            <field name="volume">15000000000</field>
            <field name="week_52_high">4878.26</field>
            <field name="week_52_low">896.11</field>
            <field name="day_high">2680.00</field>
            <field name="day_low">2580.00</field>
        </record>
        
        <!-- Demo OHLC Data for AAPL -->
        <record id="ohlc_aapl_1" model="tradingview.ohlc">
            <field name="symbol_id" ref="symbol_aapl"/>
            <field name="timestamp" eval="(datetime.now() - timedelta(days=1)).replace(hour=16, minute=0, second=0, microsecond=0)"/>
            <field name="timeframe">1d</field>
            <field name="open">173.20</field>
            <field name="high">176.80</field>
            <field name="low">172.50</field>
            <field name="close">175.50</field>
            <field name="volume">45000000</field>
        </record>
        
        <record id="ohlc_aapl_2" model="tradingview.ohlc">
            <field name="symbol_id" ref="symbol_aapl"/>
            <field name="timestamp" eval="(datetime.now() - timedelta(days=2)).replace(hour=16, minute=0, second=0, microsecond=0)"/>
            <field name="timeframe">1d</field>
            <field name="open">171.00</field>
            <field name="high">174.20</field>
            <field name="low">170.80</field>
            <field name="close">173.20</field>
            <field name="volume">42000000</field>
        </record>
        
        <!-- Demo News Article -->
        <record id="news_aapl_1" model="tradingview.news">
            <field name="symbol_id" ref="symbol_aapl"/>
            <field name="title">Apple Reports Strong Q4 Earnings</field>
            <field name="summary">Apple Inc. reported better-than-expected quarterly earnings driven by strong iPhone sales.</field>
            <field name="link">https://example.com/apple-earnings</field>
            <field name="source">Financial News</field>
            <field name="author">John Doe</field>
            <field name="published_at" eval="datetime.now() - timedelta(hours=2)"/>
            <field name="category">earnings</field>
            <field name="sentiment">positive</field>
            <field name="impact_level">high</field>
            <field name="is_breaking">True</field>
        </record>
        
        <!-- Demo Event -->
        <record id="event_aapl_1" model="tradingview.event">
            <field name="symbol_id" ref="symbol_aapl"/>
            <field name="title">Apple Q1 2024 Earnings Call</field>
            <field name="description">Apple Inc. will report Q1 2024 financial results and host earnings call.</field>
            <field name="date" eval="datetime.now() + timedelta(days=7)"/>
            <field name="event_type">event_earnings</field>
            <field name="impact_level">high</field>
            <field name="status">scheduled</field>
            <field name="estimated_eps">2.10</field>
            <field name="estimated_revenue">118500000000</field>
        </record>
        
    </data>
</odoo>
