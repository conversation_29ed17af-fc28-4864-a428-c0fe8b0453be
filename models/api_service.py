# -*- coding: utf-8 -*-

import requests
import json
import time
import logging
from datetime import datetime, timedelta
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class TradingViewAPIService(models.TransientModel):
    _name = 'tradingview.api.service'
    _description = 'API Service for Data Synchronization'

    def _get_session(self):
        """Get or create a requests session with proper headers"""
        if not hasattr(self, '_session'):
            self._session = requests.Session()
            self._session.headers.update({
                'User-Agent': 'TradingView-Clone/1.0',
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            })
        return self._session

    @property
    def session(self):
        """Property to access the requests session"""
        return self._get_session()

    # TwelveData API Integration
    def _get_twelvedata_config(self):
        """Get TwelveData API configuration"""
        return {
            'base_url': 'https://api.twelvedata.com',
            'api_key': self.env['ir.config_parameter'].sudo().get_param('tradingview.twelvedata_api_key', ''),
            'rate_limit': 8,  # requests per minute for free tier
            'timeout': 30
        }

    def sync_symbols_twelvedata(self, symbol_types=None):
        """Sync symbols from TwelveData API"""
        config = self._get_twelvedata_config()
        if not config['api_key']:
            raise UserError(_('TwelveData API key not configured'))

        sync_log = self.env['tradingview.sync_log'].create_sync_log(
            'twelvedata', 'symbols', triggered_by='manual'
        )

        try:
            # Get stock symbols
            if not symbol_types or 'stock' in symbol_types:
                self._sync_stock_symbols_twelvedata(config, sync_log)
            
            # Get forex symbols
            if not symbol_types or 'forex' in symbol_types:
                self._sync_forex_symbols_twelvedata(config, sync_log)
            
            # Get crypto symbols
            if not symbol_types or 'crypto' in symbol_types:
                self._sync_crypto_symbols_twelvedata(config, sync_log)

            sync_log.mark_completed('success')
            return sync_log

        except Exception as e:
            sync_log.mark_failed(str(e))
            _logger.error(f"TwelveData symbols sync failed: {e}")
            raise

    def _sync_stock_symbols_twelvedata(self, config, sync_log):
        """Sync stock symbols from TwelveData"""
        url = f"{config['base_url']}/stocks"
        params = {
            'apikey': config['api_key'],
            'country': 'United States',
            'format': 'json'
        }

        response = self._make_api_request(url, params, config['timeout'])
        sync_log.update_progress(api_calls=1)

        if response and 'data' in response:
            symbols_data = []
            for stock in response['data'][:100]:  # Limit for demo
                symbol_data = {
                    'symbol': stock.get('symbol', ''),
                    'name': stock.get('name', ''),
                    'exchange': stock.get('exchange', ''),
                    'region': stock.get('country', 'US'),
                    'currency': stock.get('currency', 'USD'),
                    'type': 'stock',
                    'sector': stock.get('sector', ''),
                    'industry': stock.get('industry', ''),
                }
                symbols_data.append(symbol_data)

            result = self._bulk_create_symbols(symbols_data)
            sync_log.update_progress(
                processed=len(symbols_data),
                created=result['success_count'],
                failed=result['error_count']
            )

    def _sync_forex_symbols_twelvedata(self, config, sync_log):
        """Sync forex symbols from TwelveData"""
        url = f"{config['base_url']}/forex_pairs"
        params = {
            'apikey': config['api_key'],
            'format': 'json'
        }

        response = self._make_api_request(url, params, config['timeout'])
        sync_log.update_progress(api_calls=1)

        if response and 'data' in response:
            symbols_data = []
            for pair in response['data'][:50]:  # Limit for demo
                symbol_data = {
                    'symbol': pair.get('symbol', ''),
                    'name': f"{pair.get('currency_base', '')} / {pair.get('currency_quote', '')}",
                    'exchange': 'FOREX',
                    'region': 'Global',
                    'currency': pair.get('currency_quote', 'USD'),
                    'type': 'forex',
                }
                symbols_data.append(symbol_data)

            result = self._bulk_create_symbols(symbols_data)
            sync_log.update_progress(
                processed=len(symbols_data),
                created=result['success_count'],
                failed=result['error_count']
            )

    def _sync_crypto_symbols_twelvedata(self, config, sync_log):
        """Sync crypto symbols from TwelveData"""
        url = f"{config['base_url']}/cryptocurrencies"
        params = {
            'apikey': config['api_key'],
            'format': 'json'
        }

        response = self._make_api_request(url, params, config['timeout'])
        sync_log.update_progress(api_calls=1)

        if response and 'data' in response:
            symbols_data = []
            for crypto in response['data'][:50]:  # Limit for demo
                symbol_data = {
                    'symbol': crypto.get('symbol', ''),
                    'name': crypto.get('currency_name', ''),
                    'exchange': 'CRYPTO',
                    'region': 'Global',
                    'currency': 'USD',
                    'type': 'crypto',
                }
                symbols_data.append(symbol_data)

            result = self._bulk_create_symbols(symbols_data)
            sync_log.update_progress(
                processed=len(symbols_data),
                created=result['success_count'],
                failed=result['error_count']
            )

    # Binance API Integration
    def _get_binance_config(self):
        """Get Binance API configuration"""
        return {
            'base_url': 'https://api.binance.com/api/v3',
            'rate_limit': 1200,  # requests per minute
            'timeout': 30
        }

    def sync_crypto_data_binance(self, symbols=None):
        """Sync crypto data from Binance API"""
        config = self._get_binance_config()
        
        sync_log = self.env['tradingview.sync_log'].create_sync_log(
            'binance', 'ohlc', triggered_by='manual'
        )

        try:
            if not symbols:
                # Get top crypto symbols
                crypto_symbols = self.env['tradingview.symbol'].search([
                    ('type', '=', 'crypto'),
                    ('active', '=', True)
                ], limit=20)
                symbols = [s.symbol for s in crypto_symbols]

            for symbol in symbols:
                self._sync_crypto_ohlc_binance(symbol, config, sync_log)
                time.sleep(0.1)  # Rate limiting

            sync_log.mark_completed('success')
            return sync_log

        except Exception as e:
            sync_log.mark_failed(str(e))
            _logger.error(f"Binance crypto sync failed: {e}")
            raise

    def _sync_crypto_ohlc_binance(self, symbol, config, sync_log):
        """Sync OHLC data for a crypto symbol from Binance"""
        # Convert symbol format (BTCUSD -> BTCUSDT)
        binance_symbol = symbol.replace('USD', 'USDT') if symbol.endswith('USD') else symbol
        
        url = f"{config['base_url']}/klines"
        params = {
            'symbol': binance_symbol,
            'interval': '1d',
            'limit': 100
        }

        response = self._make_api_request(url, params, config['timeout'])
        sync_log.update_progress(api_calls=1)

        if response:
            symbol_obj = self.env['tradingview.symbol'].search([('symbol', '=', symbol)], limit=1)
            if not symbol_obj:
                return

            ohlc_data = []
            for kline in response:
                ohlc_data.append({
                    'symbol_id': symbol_obj.id,
                    'timestamp': datetime.fromtimestamp(kline[0] / 1000),
                    'timeframe': '1d',
                    'open': float(kline[1]),
                    'high': float(kline[2]),
                    'low': float(kline[3]),
                    'close': float(kline[4]),
                    'volume': float(kline[5]),
                    'data_source': 'binance'
                })

            result = self.env['tradingview.ohlc'].bulk_create_ohlc(ohlc_data)
            sync_log.update_progress(
                processed=len(ohlc_data),
                created=result['success_count'],
                failed=result['error_count']
            )

    # NewsAPI Integration
    def _get_newsapi_config(self):
        """Get NewsAPI configuration"""
        return {
            'base_url': 'https://newsapi.org/v2',
            'api_key': self.env['ir.config_parameter'].sudo().get_param('tradingview.newsapi_key', ''),
            'rate_limit': 1000,  # requests per day for free tier
            'timeout': 30
        }

    def sync_financial_news(self, symbols=None, days_back=1):
        """Sync financial news from NewsAPI"""
        config = self._get_newsapi_config()
        if not config['api_key']:
            raise UserError(_('NewsAPI key not configured'))

        sync_log = self.env['tradingview.sync_log'].create_sync_log(
            'newsapi', 'news', triggered_by='manual'
        )

        try:
            # Get general financial news
            self._sync_general_financial_news(config, sync_log, days_back)
            
            # Get symbol-specific news
            if symbols:
                for symbol in symbols[:10]:  # Limit to avoid rate limits
                    self._sync_symbol_news(symbol, config, sync_log, days_back)
                    time.sleep(1)  # Rate limiting

            sync_log.mark_completed('success')
            return sync_log

        except Exception as e:
            sync_log.mark_failed(str(e))
            _logger.error(f"NewsAPI sync failed: {e}")
            raise

    def _sync_general_financial_news(self, config, sync_log, days_back):
        """Sync general financial news"""
        url = f"{config['base_url']}/everything"
        from_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
        
        params = {
            'apiKey': config['api_key'],
            'q': 'finance OR stock OR market OR trading',
            'from': from_date,
            'sortBy': 'publishedAt',
            'language': 'en',
            'pageSize': 50
        }

        response = self._make_api_request(url, params, config['timeout'])
        sync_log.update_progress(api_calls=1)

        if response and 'articles' in response:
            # Create a general market symbol if it doesn't exist
            market_symbol = self.env['tradingview.symbol'].search([('symbol', '=', 'MARKET')], limit=1)
            if not market_symbol:
                market_symbol = self.env['tradingview.symbol'].create({
                    'symbol': 'MARKET',
                    'name': 'General Market News',
                    'type': 'index',
                    'exchange': 'NEWS',
                    'region': 'Global'
                })

            news_data = []
            for article in response['articles']:
                if article.get('title') and article.get('url'):
                    news_data.append({
                        'symbol_id': market_symbol.id,
                        'title': article['title'],
                        'summary': article.get('description', ''),
                        'link': article['url'],
                        'source': article.get('source', {}).get('name', 'Unknown'),
                        'author': article.get('author', ''),
                        'published_at': self._parse_datetime(article.get('publishedAt')),
                        'category': 'market'
                    })

            result = self.env['tradingview.news'].bulk_create_news(news_data)
            sync_log.update_progress(
                processed=len(news_data),
                created=result['success_count'],
                failed=result['error_count']
            )

    def _sync_symbol_news(self, symbol, config, sync_log, days_back):
        """Sync news for a specific symbol"""
        symbol_obj = self.env['tradingview.symbol'].search([('symbol', '=', symbol)], limit=1)
        if not symbol_obj:
            return

        url = f"{config['base_url']}/everything"
        from_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
        
        # Create search query based on symbol and company name
        query = f'"{symbol}" OR "{symbol_obj.name}"'
        
        params = {
            'apiKey': config['api_key'],
            'q': query,
            'from': from_date,
            'sortBy': 'publishedAt',
            'language': 'en',
            'pageSize': 20
        }

        response = self._make_api_request(url, params, config['timeout'])
        sync_log.update_progress(api_calls=1)

        if response and 'articles' in response:
            news_data = []
            for article in response['articles']:
                if article.get('title') and article.get('url'):
                    news_data.append({
                        'symbol_id': symbol_obj.id,
                        'title': article['title'],
                        'summary': article.get('description', ''),
                        'link': article['url'],
                        'source': article.get('source', {}).get('name', 'Unknown'),
                        'author': article.get('author', ''),
                        'published_at': self._parse_datetime(article.get('publishedAt')),
                        'category': 'company'
                    })

            result = self.env['tradingview.news'].bulk_create_news(news_data)
            sync_log.update_progress(
                processed=len(news_data),
                created=result['success_count'],
                failed=result['error_count']
            )

    # Utility Methods
    def _make_api_request(self, url, params=None, timeout=30, retries=3):
        """Make API request with retry logic"""
        for attempt in range(retries):
            try:
                response = self.session.get(url, params=params, timeout=timeout)
                
                if response.status_code == 429:  # Rate limit
                    wait_time = int(response.headers.get('Retry-After', 60))
                    _logger.warning(f"Rate limit hit, waiting {wait_time} seconds")
                    time.sleep(wait_time)
                    continue
                
                response.raise_for_status()
                return response.json()
                
            except requests.exceptions.RequestException as e:
                _logger.warning(f"API request failed (attempt {attempt + 1}): {e}")
                if attempt == retries - 1:
                    raise
                time.sleep(2 ** attempt)  # Exponential backoff
        
        return None

    def _parse_datetime(self, date_string):
        """Parse datetime string from API response"""
        if not date_string:
            return fields.Datetime.now()
        
        try:
            # Handle ISO format with timezone
            if 'T' in date_string:
                return datetime.fromisoformat(date_string.replace('Z', '+00:00'))
            else:
                return datetime.strptime(date_string, '%Y-%m-%d %H:%M:%S')
        except:
            return fields.Datetime.now()

    def _bulk_create_symbols(self, symbols_data):
        """Bulk create symbols with error handling"""
        created_count = 0
        error_count = 0
        
        for symbol_data in symbols_data:
            try:
                # Check if symbol already exists
                existing = self.env['tradingview.symbol'].search([
                    ('symbol', '=', symbol_data.get('symbol'))
                ], limit=1)
                
                if not existing and symbol_data.get('symbol'):
                    self.env['tradingview.symbol'].create(symbol_data)
                    created_count += 1
                    
            except Exception as e:
                error_count += 1
                _logger.error(f"Error creating symbol {symbol_data.get('symbol', 'Unknown')}: {e}")
        
        return {
            'success_count': created_count,
            'error_count': error_count
        }
