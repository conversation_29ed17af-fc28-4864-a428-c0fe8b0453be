# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging
from datetime import datetime, timedelta
from .ohlc import IMPACT_LEVEL_SELECTION, EVENT_STATUS_SELECTION

_logger = logging.getLogger(__name__)


class TradingViewEvent(models.Model):
    _name = 'tradingview.event'
    _description = 'Financial Events'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'date asc'
    _rec_name = 'title'

    # Core Fields
    symbol_id = fields.Many2one(
        'tradingview.symbol',
        string='Symbol',
        required=True,
        index=True,
        ondelete='cascade'
    )
    title = fields.Char(
        string='Title',
        required=True,
        help='Event title'
    )
    description = fields.Text(
        string='Description',
        help='Detailed event description'
    )
    date = fields.Datetime(
        string='Event Date',
        required=True,
        index=True,
        help='When the event is scheduled'
    )
    
    # Event Classification
    event_type = fields.Selection([
        # Corporate Events
        ('event_earnings', 'Earnings Report'),
        ('event_dividend', 'Dividend Payment'),
        ('event_split', 'Stock Split'),
        ('event_merger', 'Merger & Acquisition'),
        ('event_ipo', 'IPO'),
        ('event_delisting', 'Delisting'),

        # Crypto Events
        ('event_fork', 'Crypto Fork'),
        ('event_halving', 'Halving'),
        ('event_listing', 'Exchange Listing'),
        ('event_unlock', 'Token Unlock'),
        ('event_burn', 'Token Burn'),

        # Economic Events
        ('event_fed_meeting', 'Fed Meeting'),
        ('event_interest_rate', 'Interest Rate Decision'),
        ('event_gdp', 'GDP Release'),
        ('event_inflation', 'Inflation Data'),
        ('event_employment', 'Employment Data'),
        ('event_trade_balance', 'Trade Balance'),

        # Market Events
        ('event_options_expiry', 'Options Expiry'),
        ('event_futures_expiry', 'Futures Expiry'),
        ('event_index_rebalance', 'Index Rebalancing'),

        # Company Events
        ('event_conference', 'Conference Call'),
        ('event_presentation', 'Investor Presentation'),
        ('event_agm', 'Annual General Meeting'),
        ('event_product_launch', 'Product Launch'),

        # Other
        ('event_other', 'Other'),
    ], string='Event Type', required=True, default='event_other')
    
    impact_level = fields.Selection(
        IMPACT_LEVEL_SELECTION,
        string='Impact Level',
        required=True,
        default='medium'
    )
    
    # Status and Timing
    status = fields.Selection(
        EVENT_STATUS_SELECTION,
        string='Status',
        default='scheduled'
    )
    
    is_recurring = fields.Boolean(string='Recurring Event', default=False)
    recurrence_pattern = fields.Char(
        string='Recurrence Pattern',
        help='Pattern for recurring events (e.g., quarterly, monthly)'
    )
    
    # Additional Information
    location = fields.Char(string='Location')
    timezone = fields.Char(string='Timezone', default='UTC')
    duration_minutes = fields.Integer(string='Duration (Minutes)')
    
    # External Links
    link = fields.Char(string='Event Link')
    webcast_link = fields.Char(string='Webcast Link')
    document_link = fields.Char(string='Document Link')
    
    # Estimates and Actuals (for earnings)
    estimated_eps = fields.Float(string='Estimated EPS', digits=(16, 4))
    actual_eps = fields.Float(string='Actual EPS', digits=(16, 4))
    estimated_revenue = fields.Float(string='Estimated Revenue', digits=(16, 2))
    actual_revenue = fields.Float(string='Actual Revenue', digits=(16, 2))
    
    # Computed Fields
    is_upcoming = fields.Boolean(
        string='Upcoming',
        compute='_compute_timing',
        store=True
    )
    is_today = fields.Boolean(
        string='Today',
        compute='_compute_timing',
        store=True
    )
    days_until = fields.Integer(
        string='Days Until',
        compute='_compute_timing',
        store=True
    )
    time_until = fields.Char(
        string='Time Until',
        compute='_compute_timing'
    )
    
    # Metadata
    created_date = fields.Datetime(string='Created Date', default=fields.Datetime.now)
    source = fields.Char(string='Data Source')
    external_id = fields.Char(string='External ID', help='ID from external data source')
    
    @api.depends('date')
    def _compute_timing(self):
        now = fields.Datetime.now()
        today = now.date()
        
        for record in self:
            if record.date:
                event_date = record.date.date() if record.date else None
                
                record.is_upcoming = record.date > now
                record.is_today = event_date == today
                
                if record.date > now:
                    delta = record.date - now
                    record.days_until = delta.days
                    
                    if delta.days == 0:
                        hours = delta.seconds // 3600
                        minutes = (delta.seconds % 3600) // 60
                        record.time_until = f"{hours}h {minutes}m"
                    elif delta.days == 1:
                        record.time_until = "Tomorrow"
                    else:
                        record.time_until = f"{delta.days} days"
                else:
                    record.days_until = 0
                    record.time_until = "Past"
            else:
                record.is_upcoming = False
                record.is_today = False
                record.days_until = 0
                record.time_until = ""
    
    @api.constrains('date')
    def _check_event_date(self):
        for record in self:
            if record.date and record.is_recurring and record.date < fields.Datetime.now():
                # Allow past dates for recurring events
                pass
            elif record.date and record.date < (fields.Datetime.now() - timedelta(days=365)):
                raise ValidationError(_('Event date cannot be more than 1 year in the past.'))
    
    @api.model
    def get_upcoming_events(self, symbol_id=None, days_ahead=30, limit=10):
        """Get upcoming events"""
        end_date = fields.Datetime.now() + timedelta(days=days_ahead)
        
        domain = [
            ('date', '>=', fields.Datetime.now()),
            ('date', '<=', end_date),
            ('status', 'in', ['scheduled', 'in_progress'])
        ]
        
        if symbol_id:
            domain.append(('symbol_id', '=', symbol_id))
        
        return self.search(domain, order='date asc', limit=limit)
    
    @api.model
    def get_today_events(self, symbol_id=None):
        """Get today's events"""
        today_start = fields.Datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        today_end = today_start + timedelta(days=1)
        
        domain = [
            ('date', '>=', today_start),
            ('date', '<', today_end),
            ('status', 'in', ['scheduled', 'in_progress'])
        ]
        
        if symbol_id:
            domain.append(('symbol_id', '=', symbol_id))
        
        return self.search(domain, order='date asc')
    
    @api.model
    def get_earnings_calendar(self, start_date=None, end_date=None, limit=50):
        """Get earnings calendar"""
        if not start_date:
            start_date = fields.Datetime.now()
        if not end_date:
            end_date = start_date + timedelta(days=30)
        
        domain = [
            ('event_type', '=', 'earnings'),
            ('date', '>=', start_date),
            ('date', '<=', end_date),
            ('status', 'in', ['scheduled', 'in_progress'])
        ]
        
        return self.search(domain, order='date asc', limit=limit)
    
    @api.model
    def bulk_create_events(self, event_data_list):
        """Bulk create events with duplicate checking"""
        created_records = []
        errors = []
        duplicates = []
        
        for data in event_data_list:
            try:
                # Check for duplicates by symbol, type, and date
                existing = self.search([
                    ('symbol_id', '=', data.get('symbol_id')),
                    ('event_type', '=', data.get('event_type')),
                    ('date', '=', data.get('date')),
                    ('title', '=', data.get('title'))
                ], limit=1)
                
                if existing:
                    duplicates.append(data)
                    continue
                
                record = self.create(data)
                created_records.append(record)
                
            except Exception as e:
                errors.append({
                    'data': data,
                    'error': str(e)
                })
                _logger.error(f"Error creating event record: {e}")
        
        return {
            'created': created_records,
            'errors': errors,
            'duplicates': duplicates,
            'success_count': len(created_records),
            'error_count': len(errors),
            'duplicate_count': len(duplicates)
        }
    
    def mark_completed(self):
        """Mark event as completed"""
        self.status = 'completed'
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Event Completed'),
                'message': _('Event "%s" marked as completed') % self.title,
                'type': 'success',
            }
        }
    
    def mark_cancelled(self):
        """Mark event as cancelled"""
        self.status = 'cancelled'
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Event Cancelled'),
                'message': _('Event "%s" marked as cancelled') % self.title,
                'type': 'warning',
            }
        }
    
    def update_actuals(self, actual_eps=None, actual_revenue=None):
        """Update actual values for earnings events"""
        if self.event_type == 'earnings':
            update_vals = {}
            if actual_eps is not None:
                update_vals['actual_eps'] = actual_eps
            if actual_revenue is not None:
                update_vals['actual_revenue'] = actual_revenue
            
            if update_vals:
                self.write(update_vals)
                self.mark_completed()
    
    def get_event_summary(self):
        """Get event summary for display"""
        self.ensure_one()
        
        summary = {
            'title': self.title,
            'type': dict(self._fields['event_type'].selection).get(self.event_type),
            'date': self.date,
            'impact': self.impact_level,
            'status': self.status,
            'time_until': self.time_until,
            'is_upcoming': self.is_upcoming,
        }
        
        # Add specific data for earnings
        if self.event_type == 'earnings':
            summary.update({
                'estimated_eps': self.estimated_eps,
                'actual_eps': self.actual_eps,
                'estimated_revenue': self.estimated_revenue,
                'actual_revenue': self.actual_revenue,
            })
        
        return summary
    
    @api.model
    def create_recurring_events(self, base_event_id, occurrences=4):
        """Create recurring events based on a base event"""
        base_event = self.browse(base_event_id)
        if not base_event.is_recurring:
            return []
        
        created_events = []
        
        for i in range(1, occurrences + 1):
            # Calculate next occurrence date (quarterly for earnings)
            if base_event.event_type == 'earnings':
                next_date = base_event.date + timedelta(days=90 * i)
            else:
                next_date = base_event.date + timedelta(days=30 * i)  # Monthly default
            
            event_data = {
                'symbol_id': base_event.symbol_id.id,
                'title': base_event.title,
                'description': base_event.description,
                'date': next_date,
                'event_type': base_event.event_type,
                'impact_level': base_event.impact_level,
                'is_recurring': True,
                'recurrence_pattern': base_event.recurrence_pattern,
                'source': base_event.source,
            }
            
            created_event = self.create(event_data)
            created_events.append(created_event)
        
        return created_events
