# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging
from datetime import datetime, timedelta
import json
from .ohlc import SYNC_STATUS_SELECTION

_logger = logging.getLogger(__name__)


class TradingViewSyncLog(models.Model):
    _name = 'tradingview.sync_log'
    _description = 'Data Synchronization Log'
    _order = 'start_time desc'
    _rec_name = 'display_name'

    # Core Fields
    api_name = fields.Selection([
        ('twelvedata', 'TwelveData API'),
        ('binance', 'Binance API'),
        ('newsapi', 'NewsAPI'),
        ('yahoo_finance', 'Yahoo Finance'),
        ('financial_modeling_prep', 'Financial Modeling Prep'),
        ('alpha_vantage', 'Alpha Vantage'),
        ('polygon', 'Polygon.io'),
        ('iex_cloud', 'IEX Cloud'),
        ('custom_api', 'Custom API'),
    ], string='API Name', required=True)
    
    sync_type = fields.Selection([
        ('symbols', 'Symbol List'),
        ('ohlc', 'OHLC Data'),
        ('technical', 'Technical Indicators'),
        ('news', 'News Articles'),
        ('events', 'Financial Events'),
        ('real_time', 'Real-time Data'),
        ('historical', 'Historical Data'),
        ('full_sync', 'Full Synchronization'),
    ], string='Sync Type', required=True)
    
    # Timing
    start_time = fields.Datetime(
        string='Start Time',
        required=True,
        default=fields.Datetime.now
    )
    end_time = fields.Datetime(string='End Time')
    duration_seconds = fields.Float(
        string='Duration (Seconds)',
        compute='_compute_duration',
        store=True
    )
    
    # Status and Results
    status = fields.Selection(
        SYNC_STATUS_SELECTION,
        string='Status',
        default='running',
        required=True
    )
    
    # Metrics
    records_processed = fields.Integer(string='Records Processed', default=0)
    records_created = fields.Integer(string='Records Created', default=0)
    records_updated = fields.Integer(string='Records Updated', default=0)
    records_failed = fields.Integer(string='Records Failed', default=0)
    api_calls_made = fields.Integer(string='API Calls Made', default=0)
    
    # Error Handling
    error_message = fields.Text(string='Error Message')
    error_details = fields.Text(string='Error Details')
    warnings = fields.Text(string='Warnings')
    
    # Configuration
    parameters = fields.Text(
        string='Parameters',
        help='JSON string of sync parameters'
    )
    symbols_synced = fields.Text(
        string='Symbols Synced',
        help='List of symbols that were synced'
    )
    
    # Rate Limiting
    rate_limit_hit = fields.Boolean(string='Rate Limit Hit', default=False)
    rate_limit_reset_time = fields.Datetime(string='Rate Limit Reset Time')
    requests_remaining = fields.Integer(string='Requests Remaining')
    
    # Computed Fields
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True
    )
    success_rate = fields.Float(
        string='Success Rate (%)',
        compute='_compute_success_rate',
        store=True,
        digits=(5, 2)
    )
    is_recent = fields.Boolean(
        string='Recent',
        compute='_compute_is_recent',
        help='Sync within last 24 hours'
    )
    
    # Metadata
    triggered_by = fields.Selection([
        ('cron', 'Cron Job'),
        ('manual', 'Manual'),
        ('api', 'API Call'),
        ('webhook', 'Webhook'),
        ('startup', 'System Startup'),
    ], string='Triggered By', default='manual')
    
    user_id = fields.Many2one(
        'res.users',
        string='User',
        help='User who triggered the sync (for manual syncs)'
    )
    
    @api.depends('api_name', 'sync_type', 'start_time', 'status')
    def _compute_display_name(self):
        for record in self:
            api_display = dict(record._fields['api_name'].selection).get(record.api_name, record.api_name)
            sync_display = dict(record._fields['sync_type'].selection).get(record.sync_type, record.sync_type)
            
            if record.start_time:
                time_str = record.start_time.strftime('%Y-%m-%d %H:%M')
                record.display_name = f"{api_display} - {sync_display} ({time_str}) - {record.status.title()}"
            else:
                record.display_name = f"{api_display} - {sync_display} - {record.status.title()}"
    
    @api.depends('start_time', 'end_time')
    def _compute_duration(self):
        for record in self:
            if record.start_time and record.end_time:
                delta = record.end_time - record.start_time
                record.duration_seconds = delta.total_seconds()
            else:
                record.duration_seconds = 0
    
    @api.depends('records_processed', 'records_failed')
    def _compute_success_rate(self):
        for record in self:
            if record.records_processed > 0:
                successful = record.records_processed - record.records_failed
                record.success_rate = (successful / record.records_processed) * 100
            else:
                record.success_rate = 0
    
    @api.depends('start_time')
    def _compute_is_recent(self):
        cutoff = fields.Datetime.now() - timedelta(hours=24)
        for record in self:
            record.is_recent = record.start_time and record.start_time >= cutoff
    
    def mark_completed(self, status='success', error_message=None):
        """Mark sync as completed"""
        self.write({
            'end_time': fields.Datetime.now(),
            'status': status,
            'error_message': error_message,
        })
    
    def mark_failed(self, error_message, error_details=None):
        """Mark sync as failed"""
        self.write({
            'end_time': fields.Datetime.now(),
            'status': 'failure',
            'error_message': error_message,
            'error_details': error_details,
        })
    
    def add_warning(self, warning_message):
        """Add a warning to the sync log"""
        current_warnings = self.warnings or ''
        timestamp = fields.Datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        new_warning = f"[{timestamp}] {warning_message}\n"
        self.warnings = current_warnings + new_warning
    
    def update_progress(self, processed=0, created=0, updated=0, failed=0, api_calls=0):
        """Update sync progress"""
        self.write({
            'records_processed': self.records_processed + processed,
            'records_created': self.records_created + created,
            'records_updated': self.records_updated + updated,
            'records_failed': self.records_failed + failed,
            'api_calls_made': self.api_calls_made + api_calls,
        })
    
    def set_rate_limit_info(self, hit=False, reset_time=None, remaining=None):
        """Set rate limit information"""
        self.write({
            'rate_limit_hit': hit,
            'rate_limit_reset_time': reset_time,
            'requests_remaining': remaining,
        })
    
    @api.model
    def create_sync_log(self, api_name, sync_type, parameters=None, triggered_by='manual', user_id=None):
        """Create a new sync log entry"""
        vals = {
            'api_name': api_name,
            'sync_type': sync_type,
            'triggered_by': triggered_by,
            'user_id': user_id or self.env.user.id,
        }
        
        if parameters:
            vals['parameters'] = json.dumps(parameters) if isinstance(parameters, dict) else str(parameters)
        
        return self.create(vals)
    
    @api.model
    def get_sync_statistics(self, days=7):
        """Get sync statistics for the last N days"""
        cutoff_date = fields.Datetime.now() - timedelta(days=days)
        
        logs = self.search([('start_time', '>=', cutoff_date)])
        
        stats = {
            'total_syncs': len(logs),
            'successful_syncs': len(logs.filtered(lambda l: l.status == 'success')),
            'failed_syncs': len(logs.filtered(lambda l: l.status == 'failure')),
            'total_records_processed': sum(logs.mapped('records_processed')),
            'total_api_calls': sum(logs.mapped('api_calls_made')),
            'average_duration': sum(logs.mapped('duration_seconds')) / len(logs) if logs else 0,
            'rate_limit_hits': len(logs.filtered('rate_limit_hit')),
        }
        
        # Success rate
        if stats['total_syncs'] > 0:
            stats['success_rate'] = (stats['successful_syncs'] / stats['total_syncs']) * 100
        else:
            stats['success_rate'] = 0
        
        # API breakdown
        api_stats = {}
        for api_name in logs.mapped('api_name'):
            api_logs = logs.filtered(lambda l: l.api_name == api_name)
            api_stats[api_name] = {
                'total': len(api_logs),
                'successful': len(api_logs.filtered(lambda l: l.status == 'success')),
                'failed': len(api_logs.filtered(lambda l: l.status == 'failure')),
                'api_calls': sum(api_logs.mapped('api_calls_made')),
            }
        
        stats['api_breakdown'] = api_stats
        
        return stats
    
    @api.model
    def get_recent_failures(self, limit=10):
        """Get recent sync failures for monitoring"""
        return self.search([
            ('status', '=', 'failure'),
        ], order='start_time desc', limit=limit)
    
    @api.model
    def cleanup_old_logs(self, days_to_keep=30):
        """Clean up old sync logs"""
        cutoff_date = fields.Datetime.now() - timedelta(days=days_to_keep)
        
        old_logs = self.search([
            ('start_time', '<', cutoff_date),
            ('status', 'in', ['success', 'failure', 'cancelled'])
        ])
        
        count = len(old_logs)
        old_logs.unlink()
        
        _logger.info(f"Cleaned up {count} old sync logs older than {days_to_keep} days")
        return count
    
    def retry_sync(self):
        """Retry a failed sync"""
        if self.status != 'failure':
            raise ValidationError(_('Only failed syncs can be retried'))
        
        # Create a new sync log with the same parameters
        new_sync = self.create({
            'api_name': self.api_name,
            'sync_type': self.sync_type,
            'parameters': self.parameters,
            'triggered_by': 'manual',
            'user_id': self.env.user.id,
        })
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Retry Sync'),
            'res_model': 'tradingview.sync_log',
            'res_id': new_sync.id,
            'view_mode': 'form',
            'target': 'current',
        }
    
    def get_sync_summary(self):
        """Get sync summary for display"""
        self.ensure_one()
        
        summary = {
            'api_name': dict(self._fields['api_name'].selection).get(self.api_name),
            'sync_type': dict(self._fields['sync_type'].selection).get(self.sync_type),
            'status': self.status,
            'duration': self.duration_seconds,
            'records_processed': self.records_processed,
            'success_rate': self.success_rate,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'error_message': self.error_message,
            'rate_limit_hit': self.rate_limit_hit,
        }
        
        return summary
