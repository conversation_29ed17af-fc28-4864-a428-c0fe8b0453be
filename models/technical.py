# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging
from datetime import datetime, timedelta
from .ohlc import TIMEFRAME_SELECTION, SIGNAL_SELECTION

_logger = logging.getLogger(__name__)


class TradingViewTechnical(models.Model):
    _name = 'tradingview.technical'
    _description = 'Technical Indicators'
    _order = 'timestamp desc, indicator asc'
    _rec_name = 'display_name'
    _sql_constraints = [
        ('symbol_indicator_timestamp_unique', 
         'UNIQUE(symbol_id, indicator, timestamp, timeframe)', 
         'Technical indicator must be unique per symbol, indicator, timestamp, and timeframe!'),
    ]

    # Core Fields
    symbol_id = fields.Many2one(
        'tradingview.symbol',
        string='Symbol',
        required=True,
        index=True,
        ondelete='cascade'
    )
    indicator = fields.Selection([
        # Moving Averages
        ('sma_20', 'SMA 20'),
        ('sma_50', 'SMA 50'),
        ('sma_100', 'SMA 100'),
        ('sma_200', 'SMA 200'),
        ('ema_12', 'EMA 12'),
        ('ema_26', 'EMA 26'),
        ('ema_50', 'EMA 50'),
        ('ema_200', 'EMA 200'),
        
        # Oscillators
        ('rsi', 'RSI'),
        ('rsi_14', 'RSI 14'),
        ('stoch_k', 'Stochastic %K'),
        ('stoch_d', 'Stochastic %D'),
        ('williams_r', 'Williams %R'),
        ('cci', 'Commodity Channel Index'),
        
        # MACD
        ('macd_line', 'MACD Line'),
        ('macd_signal', 'MACD Signal'),
        ('macd_histogram', 'MACD Histogram'),
        
        # Bollinger Bands
        ('bb_upper', 'Bollinger Upper'),
        ('bb_middle', 'Bollinger Middle'),
        ('bb_lower', 'Bollinger Lower'),
        ('bb_width', 'Bollinger Width'),
        
        # Volume Indicators
        ('volume_sma', 'Volume SMA'),
        ('obv', 'On Balance Volume'),
        ('ad_line', 'Accumulation/Distribution'),
        ('mfi', 'Money Flow Index'),
        
        # Trend Indicators
        ('adx', 'Average Directional Index'),
        ('aroon_up', 'Aroon Up'),
        ('aroon_down', 'Aroon Down'),
        ('parabolic_sar', 'Parabolic SAR'),
        
        # Support/Resistance
        ('pivot_point', 'Pivot Point'),
        ('resistance_1', 'Resistance 1'),
        ('resistance_2', 'Resistance 2'),
        ('support_1', 'Support 1'),
        ('support_2', 'Support 2'),
        
        # Custom Indicators
        ('custom_indicator', 'Custom Indicator'),
    ], string='Indicator', required=True)
    
    value = fields.Float(
        string='Value',
        required=True,
        digits=(16, 6),
        help='Calculated indicator value'
    )
    
    timestamp = fields.Datetime(
        string='Timestamp',
        required=True,
        index=True,
        help='Timestamp for this indicator calculation'
    )
    
    timeframe = fields.Selection(
        TIMEFRAME_SELECTION,
        string='Timeframe',
        required=True,
        default='1d'
    )
    
    # Additional Parameters
    period = fields.Integer(
        string='Period',
        help='Period used for calculation (e.g., 14 for RSI-14)'
    )
    custom_name = fields.Char(
        string='Custom Name',
        help='Custom name for custom indicators'
    )
    parameters = fields.Text(
        string='Parameters',
        help='JSON string of additional parameters used for calculation'
    )
    
    # Signal Information
    signal = fields.Selection(
        SIGNAL_SELECTION,
        string='Signal',
        help='Trading signal based on indicator value'
    )
    
    signal_strength = fields.Float(
        string='Signal Strength',
        digits=(5, 2),
        help='Signal strength from 0 to 100'
    )
    
    # Computed Fields
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True
    )
    
    indicator_category = fields.Selection([
        ('trend', 'Trend'),
        ('momentum', 'Momentum'),
        ('volume', 'Volume'),
        ('volatility', 'Volatility'),
        ('support_resistance', 'Support/Resistance'),
    ], string='Category', compute='_compute_indicator_category', store=True)
    
    # Metadata
    created_date = fields.Datetime(string='Created Date', default=fields.Datetime.now)
    calculation_method = fields.Char(string='Calculation Method')
    data_source = fields.Char(string='Data Source')
    
    @api.depends('symbol_id', 'indicator', 'timestamp', 'timeframe')
    def _compute_display_name(self):
        for record in self:
            if record.symbol_id and record.indicator:
                indicator_name = record.custom_name or dict(record._fields['indicator'].selection).get(record.indicator, record.indicator)
                record.display_name = f"{record.symbol_id.symbol} - {indicator_name} ({record.timeframe})"
            else:
                record.display_name = 'Technical Indicator'
    
    @api.depends('indicator')
    def _compute_indicator_category(self):
        category_mapping = {
            # Trend indicators
            'sma_20': 'trend', 'sma_50': 'trend', 'sma_100': 'trend', 'sma_200': 'trend',
            'ema_12': 'trend', 'ema_26': 'trend', 'ema_50': 'trend', 'ema_200': 'trend',
            'macd_line': 'trend', 'macd_signal': 'trend', 'macd_histogram': 'trend',
            'adx': 'trend', 'aroon_up': 'trend', 'aroon_down': 'trend', 'parabolic_sar': 'trend',
            
            # Momentum indicators
            'rsi': 'momentum', 'rsi_14': 'momentum',
            'stoch_k': 'momentum', 'stoch_d': 'momentum',
            'williams_r': 'momentum', 'cci': 'momentum', 'mfi': 'momentum',
            
            # Volume indicators
            'volume_sma': 'volume', 'obv': 'volume', 'ad_line': 'volume',
            
            # Volatility indicators
            'bb_upper': 'volatility', 'bb_middle': 'volatility', 'bb_lower': 'volatility', 'bb_width': 'volatility',
            
            # Support/Resistance
            'pivot_point': 'support_resistance', 'resistance_1': 'support_resistance', 'resistance_2': 'support_resistance',
            'support_1': 'support_resistance', 'support_2': 'support_resistance',
        }
        
        for record in self:
            record.indicator_category = category_mapping.get(record.indicator, 'trend')
    
    @api.onchange('indicator')
    def _onchange_indicator(self):
        """Set default period based on indicator"""
        period_mapping = {
            'rsi': 14, 'rsi_14': 14,
            'sma_20': 20, 'sma_50': 50, 'sma_100': 100, 'sma_200': 200,
            'ema_12': 12, 'ema_26': 26, 'ema_50': 50, 'ema_200': 200,
            'stoch_k': 14, 'stoch_d': 3,
            'williams_r': 14, 'cci': 20, 'mfi': 14, 'adx': 14,
        }
        
        if self.indicator in period_mapping:
            self.period = period_mapping[self.indicator]
    
    @api.model
    def get_latest_indicators(self, symbol_id, timeframe='1d', indicators=None):
        """Get latest values for specified indicators"""
        domain = [
            ('symbol_id', '=', symbol_id),
            ('timeframe', '=', timeframe)
        ]
        
        if indicators:
            domain.append(('indicator', 'in', indicators))
        
        # Get the latest timestamp for each indicator
        latest_indicators = {}
        for indicator in indicators or []:
            latest = self.search(domain + [('indicator', '=', indicator)], 
                               order='timestamp desc', limit=1)
            if latest:
                latest_indicators[indicator] = {
                    'value': latest.value,
                    'signal': latest.signal,
                    'signal_strength': latest.signal_strength,
                    'timestamp': latest.timestamp,
                }
        
        return latest_indicators
    
    @api.model
    def calculate_rsi_signal(self, rsi_value):
        """Calculate RSI trading signal"""
        if rsi_value >= 80:
            return 'strong_sell', 90
        elif rsi_value >= 70:
            return 'sell', 70
        elif rsi_value <= 20:
            return 'strong_buy', 90
        elif rsi_value <= 30:
            return 'buy', 70
        else:
            return 'signal_neutral', 50
    
    @api.model
    def calculate_macd_signal(self, macd_line, macd_signal):
        """Calculate MACD trading signal"""
        if macd_line > macd_signal:
            strength = min(abs(macd_line - macd_signal) * 100, 100)
            if strength > 50:
                return 'strong_buy', strength
            else:
                return 'buy', strength
        elif macd_line < macd_signal:
            strength = min(abs(macd_line - macd_signal) * 100, 100)
            if strength > 50:
                return 'strong_sell', strength
            else:
                return 'sell', strength
        else:
            return 'signal_neutral', 50
    
    @api.model
    def bulk_create_indicators(self, indicator_data_list):
        """Bulk create technical indicator records"""
        created_records = []
        errors = []
        
        for data in indicator_data_list:
            try:
                # Check if record already exists
                existing = self.search([
                    ('symbol_id', '=', data.get('symbol_id')),
                    ('indicator', '=', data.get('indicator')),
                    ('timestamp', '=', data.get('timestamp')),
                    ('timeframe', '=', data.get('timeframe', '1d'))
                ], limit=1)
                
                if not existing:
                    # Calculate signal if not provided
                    if 'signal' not in data and data.get('indicator') == 'rsi':
                        signal, strength = self.calculate_rsi_signal(data.get('value', 0))
                        data['signal'] = signal
                        data['signal_strength'] = strength
                    
                    record = self.create(data)
                    created_records.append(record)
                else:
                    # Update existing record
                    existing.write({
                        'value': data.get('value'),
                        'signal': data.get('signal'),
                        'signal_strength': data.get('signal_strength'),
                        'parameters': data.get('parameters'),
                        'data_source': data.get('data_source'),
                    })
                    created_records.append(existing)
                    
            except Exception as e:
                errors.append({
                    'data': data,
                    'error': str(e)
                })
                _logger.error(f"Error creating technical indicator record: {e}")
        
        return {
            'created': created_records,
            'errors': errors,
            'success_count': len(created_records),
            'error_count': len(errors)
        }
    
    def get_indicator_summary(self):
        """Get a summary of the indicator for display"""
        self.ensure_one()
        
        summary = {
            'name': self.custom_name or dict(self._fields['indicator'].selection).get(self.indicator, self.indicator),
            'value': self.value,
            'signal': self.signal,
            'signal_strength': self.signal_strength,
            'category': self.indicator_category,
            'timestamp': self.timestamp,
        }
        
        # Add interpretation based on indicator type
        if self.indicator in ['rsi', 'rsi_14']:
            if self.value >= 70:
                summary['interpretation'] = 'Overbought'
            elif self.value <= 30:
                summary['interpretation'] = 'Oversold'
            else:
                summary['interpretation'] = 'Normal'
        
        return summary

    @api.model
    def _calculate_rsi(self, symbol_id, period=14, timeframe='1d'):
        """Calculate RSI (Relative Strength Index) for a symbol"""
        try:
            # Get recent OHLC data
            ohlc_records = self.env['tradingview.ohlc'].search([
                ('symbol_id', '=', symbol_id),
                ('timeframe', '=', timeframe)
            ], order='timestamp desc', limit=period + 10)

            if len(ohlc_records) < period + 1:
                return False

            # Calculate price changes
            prices = [record.close for record in reversed(ohlc_records)]
            gains = []
            losses = []

            for i in range(1, len(prices)):
                change = prices[i] - prices[i-1]
                if change > 0:
                    gains.append(change)
                    losses.append(0)
                else:
                    gains.append(0)
                    losses.append(abs(change))

            # Calculate average gains and losses
            if len(gains) >= period:
                avg_gain = sum(gains[-period:]) / period
                avg_loss = sum(losses[-period:]) / period

                if avg_loss == 0:
                    rsi = 100
                else:
                    rs = avg_gain / avg_loss
                    rsi = 100 - (100 / (1 + rs))

                # Determine signal
                if rsi >= 70:
                    signal = 'sell'
                    signal_strength = min((rsi - 70) / 30, 1.0)
                elif rsi <= 30:
                    signal = 'buy'
                    signal_strength = min((30 - rsi) / 30, 1.0)
                else:
                    signal = 'signal_neutral'
                    signal_strength = 0.0

                # Create or update RSI record
                latest_ohlc = ohlc_records[0]
                self._create_or_update_indicator(
                    symbol_id, 'rsi_14', rsi, signal, signal_strength,
                    latest_ohlc.timestamp, timeframe
                )

                return rsi

        except Exception as e:
            _logger.error(f"Error calculating RSI for symbol {symbol_id}: {e}")
            return False

    @api.model
    def _calculate_moving_averages(self, symbol_id, timeframe='1d'):
        """Calculate various moving averages for a symbol"""
        try:
            # Get recent OHLC data (enough for SMA 200)
            ohlc_records = self.env['tradingview.ohlc'].search([
                ('symbol_id', '=', symbol_id),
                ('timeframe', '=', timeframe)
            ], order='timestamp desc', limit=220)

            if len(ohlc_records) < 20:
                return False

            prices = [record.close for record in reversed(ohlc_records)]
            latest_timestamp = ohlc_records[0].timestamp

            # Calculate SMAs
            sma_periods = [20, 50, 100, 200]
            for period in sma_periods:
                if len(prices) >= period:
                    sma = sum(prices[-period:]) / period

                    # Determine signal based on price vs SMA
                    current_price = prices[-1]
                    if current_price > sma:
                        signal = 'buy'
                        signal_strength = min((current_price - sma) / sma, 0.1) * 10
                    else:
                        signal = 'sell'
                        signal_strength = min((sma - current_price) / sma, 0.1) * 10

                    self._create_or_update_indicator(
                        symbol_id, f'sma_{period}', sma, signal, signal_strength,
                        latest_timestamp, timeframe
                    )

            # Calculate EMAs
            ema_periods = [12, 26, 50, 200]
            for period in ema_periods:
                if len(prices) >= period:
                    ema = self._calculate_ema(prices, period)

                    # Determine signal
                    current_price = prices[-1]
                    if current_price > ema:
                        signal = 'buy'
                        signal_strength = min((current_price - ema) / ema, 0.1) * 10
                    else:
                        signal = 'sell'
                        signal_strength = min((ema - current_price) / ema, 0.1) * 10

                    self._create_or_update_indicator(
                        symbol_id, f'ema_{period}', ema, signal, signal_strength,
                        latest_timestamp, timeframe
                    )

            return True

        except Exception as e:
            _logger.error(f"Error calculating moving averages for symbol {symbol_id}: {e}")
            return False

    @api.model
    def _calculate_macd(self, symbol_id, timeframe='1d'):
        """Calculate MACD (Moving Average Convergence Divergence) for a symbol"""
        try:
            # Get recent OHLC data
            ohlc_records = self.env['tradingview.ohlc'].search([
                ('symbol_id', '=', symbol_id),
                ('timeframe', '=', timeframe)
            ], order='timestamp desc', limit=50)

            if len(ohlc_records) < 26:
                return False

            prices = [record.close for record in reversed(ohlc_records)]
            latest_timestamp = ohlc_records[0].timestamp

            # Calculate EMAs
            ema_12 = self._calculate_ema(prices, 12)
            ema_26 = self._calculate_ema(prices, 26)

            # MACD Line = EMA(12) - EMA(26)
            macd_line = ema_12 - ema_26

            # Get previous MACD values for signal line calculation
            macd_values = []
            for i in range(min(9, len(prices))):
                if len(prices) >= 26 + i:
                    subset_prices = prices[:-(i) if i > 0 else len(prices)]
                    if len(subset_prices) >= 26:
                        ema12 = self._calculate_ema(subset_prices, 12)
                        ema26 = self._calculate_ema(subset_prices, 26)
                        macd_values.append(ema12 - ema26)

            # Signal Line = EMA(9) of MACD Line
            if len(macd_values) >= 9:
                signal_line = self._calculate_ema(list(reversed(macd_values)), 9)
            else:
                signal_line = macd_line

            # MACD Histogram = MACD Line - Signal Line
            histogram = macd_line - signal_line

            # Determine signals
            if macd_line > signal_line:
                signal = 'buy'
                signal_strength = min(abs(histogram) / abs(macd_line) if macd_line != 0 else 0, 1.0)
            else:
                signal = 'sell'
                signal_strength = min(abs(histogram) / abs(macd_line) if macd_line != 0 else 0, 1.0)

            # Create indicator records
            self._create_or_update_indicator(
                symbol_id, 'macd_line', macd_line, signal, signal_strength,
                latest_timestamp, timeframe
            )
            self._create_or_update_indicator(
                symbol_id, 'macd_signal', signal_line, signal, signal_strength,
                latest_timestamp, timeframe
            )
            self._create_or_update_indicator(
                symbol_id, 'macd_histogram', histogram, signal, signal_strength,
                latest_timestamp, timeframe
            )

            return True

        except Exception as e:
            _logger.error(f"Error calculating MACD for symbol {symbol_id}: {e}")
            return False

    def _calculate_ema(self, prices, period):
        """Calculate Exponential Moving Average"""
        if len(prices) < period:
            return sum(prices) / len(prices)

        multiplier = 2 / (period + 1)
        ema = sum(prices[:period]) / period  # Start with SMA

        for price in prices[period:]:
            ema = (price * multiplier) + (ema * (1 - multiplier))

        return ema

    def _create_or_update_indicator(self, symbol_id, indicator, value, signal, signal_strength, timestamp, timeframe):
        """Create or update a technical indicator record"""
        existing = self.search([
            ('symbol_id', '=', symbol_id),
            ('indicator', '=', indicator),
            ('timeframe', '=', timeframe),
            ('timestamp', '=', timestamp)
        ], limit=1)

        data = {
            'symbol_id': symbol_id,
            'indicator': indicator,
            'value': value,
            'signal': signal,
            'signal_strength': signal_strength,
            'timestamp': timestamp,
            'timeframe': timeframe,
        }

        if existing:
            existing.write(data)
        else:
            self.create(data)
