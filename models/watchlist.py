# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
import logging
from .ohlc import WATCHLIST_CATEGORY_SELECTION

_logger = logging.getLogger(__name__)


class TradingViewWatchlist(models.Model):
    _name = 'tradingview.watchlist'
    _description = 'User Watchlist'
    _order = 'added_date desc'
    _rec_name = 'display_name'
    _sql_constraints = [
        ('user_symbol_unique', 'UNIQUE(user_id, symbol_id)', 
         'Symbol is already in your watchlist!'),
    ]

    # Core Fields
    user_id = fields.Many2one(
        'res.users',
        string='User',
        required=True,
        index=True,
        default=lambda self: self.env.user,
        ondelete='cascade'
    )
    symbol_id = fields.Many2one(
        'tradingview.symbol',
        string='Symbol',
        required=True,
        index=True,
        ondelete='cascade'
    )
    added_date = fields.Datetime(
        string='Added Date',
        default=fields.Datetime.now,
        required=True
    )
    
    # Organization
    category = fields.Selection(
        WATCHLIST_CATEGORY_SELECTION,
        string='Category',
        default='stocks'
    )
    
    custom_category = fields.Char(
        string='Custom Category',
        help='Custom category name when category is "custom"'
    )
    
    # User Preferences
    notes = fields.Text(string='Notes', help='Personal notes about this symbol')
    target_price = fields.Float(
        string='Target Price',
        digits=(16, 6),
        help='User\'s target price for this symbol'
    )
    stop_loss = fields.Float(
        string='Stop Loss',
        digits=(16, 6),
        help='User\'s stop loss price'
    )
    
    # Alerts
    price_alert_enabled = fields.Boolean(string='Price Alert', default=False)
    price_alert_above = fields.Float(
        string='Alert Above',
        digits=(16, 6),
        help='Alert when price goes above this value'
    )
    price_alert_below = fields.Float(
        string='Alert Below',
        digits=(16, 6),
        help='Alert when price goes below this value'
    )
    volume_alert_enabled = fields.Boolean(string='Volume Alert', default=False)
    volume_alert_threshold = fields.Float(
        string='Volume Threshold',
        help='Alert when volume exceeds this threshold'
    )
    
    # Computed Fields
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True
    )
    current_price = fields.Float(
        string='Current Price',
        related='symbol_id.current_price',
        readonly=True
    )
    daily_change = fields.Float(
        string='Daily Change',
        related='symbol_id.daily_change',
        readonly=True
    )
    daily_change_percent = fields.Float(
        string='Daily Change %',
        related='symbol_id.daily_change_percent',
        readonly=True
    )
    
    # Performance Tracking
    price_when_added = fields.Float(
        string='Price When Added',
        digits=(16, 6),
        help='Symbol price when added to watchlist'
    )
    performance_since_added = fields.Float(
        string='Performance Since Added',
        compute='_compute_performance',
        digits=(16, 4),
        help='Percentage change since added to watchlist'
    )
    
    # Status
    is_active = fields.Boolean(string='Active', default=True)
    last_viewed = fields.Datetime(string='Last Viewed')
    view_count = fields.Integer(string='View Count', default=0)
    
    @api.depends('user_id', 'symbol_id')
    def _compute_display_name(self):
        for record in self:
            if record.symbol_id:
                record.display_name = f"{record.symbol_id.symbol} - {record.symbol_id.name}"
            else:
                record.display_name = 'Watchlist Item'
    
    @api.depends('price_when_added', 'current_price')
    def _compute_performance(self):
        for record in self:
            if record.price_when_added and record.current_price:
                change = record.current_price - record.price_when_added
                record.performance_since_added = (change / record.price_when_added) * 100
            else:
                record.performance_since_added = 0
    
    @api.model
    def create(self, vals):
        # Set price when added if not provided
        if 'price_when_added' not in vals and 'symbol_id' in vals:
            symbol = self.env['tradingview.symbol'].browse(vals['symbol_id'])
            vals['price_when_added'] = symbol.current_price or 0
        
        # Auto-detect category based on symbol type
        if 'category' not in vals and 'symbol_id' in vals:
            symbol = self.env['tradingview.symbol'].browse(vals['symbol_id'])
            category_mapping = {
                'stock': 'stocks',
                'crypto': 'crypto',
                'forex': 'forex',
                'commodity': 'commodities',
                'index': 'indices',
                'etf': 'stocks',
            }
            vals['category'] = category_mapping.get(symbol.type, 'stocks')
        
        return super().create(vals)
    
    @api.constrains('target_price', 'stop_loss', 'current_price')
    def _check_price_logic(self):
        for record in self:
            if record.target_price and record.stop_loss and record.target_price <= record.stop_loss:
                raise ValidationError(_('Target price must be higher than stop loss price.'))
    
    @api.constrains('price_alert_above', 'price_alert_below')
    def _check_alert_logic(self):
        for record in self:
            if (record.price_alert_above and record.price_alert_below and 
                record.price_alert_above <= record.price_alert_below):
                raise ValidationError(_('Alert above price must be higher than alert below price.'))
    
    def toggle_price_alert(self):
        """Toggle price alert on/off"""
        self.price_alert_enabled = not self.price_alert_enabled
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Price Alert'),
                'message': _('Price alert %s for %s') % (
                    'enabled' if self.price_alert_enabled else 'disabled',
                    self.symbol_id.symbol
                ),
                'type': 'success',
            }
        }
    
    def remove_from_watchlist(self):
        """Remove symbol from watchlist"""
        symbol_name = self.symbol_id.symbol
        self.unlink()
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Removed from Watchlist'),
                'message': _('%s removed from your watchlist') % symbol_name,
                'type': 'success',
            }
        }
    
    def update_last_viewed(self):
        """Update last viewed timestamp and increment view count"""
        self.write({
            'last_viewed': fields.Datetime.now(),
            'view_count': self.view_count + 1
        })
    
    @api.model
    def add_to_watchlist(self, symbol_id, user_id=None, category=None):
        """Add a symbol to user's watchlist"""
        if not user_id:
            user_id = self.env.user.id
        
        # Check if already in watchlist
        existing = self.search([
            ('user_id', '=', user_id),
            ('symbol_id', '=', symbol_id)
        ], limit=1)
        
        if existing:
            raise UserError(_('Symbol is already in your watchlist'))
        
        symbol = self.env['tradingview.symbol'].browse(symbol_id)
        
        vals = {
            'user_id': user_id,
            'symbol_id': symbol_id,
            'category': category,
        }
        
        watchlist_item = self.create(vals)
        
        return {
            'watchlist_item': watchlist_item,
            'message': _('%s added to your watchlist') % symbol.symbol
        }
    
    @api.model
    def get_user_watchlist(self, user_id=None, category=None, limit=None):
        """Get user's watchlist"""
        if not user_id:
            user_id = self.env.user.id
        
        domain = [
            ('user_id', '=', user_id),
            ('is_active', '=', True)
        ]
        
        if category:
            domain.append(('category', '=', category))
        
        return self.search(domain, order='added_date desc', limit=limit)
    
    @api.model
    def get_watchlist_categories(self, user_id=None):
        """Get user's watchlist categories with counts"""
        if not user_id:
            user_id = self.env.user.id
        
        categories = self.read_group(
            domain=[('user_id', '=', user_id), ('is_active', '=', True)],
            fields=['category'],
            groupby=['category']
        )
        
        result = []
        for cat in categories:
            category_name = cat['category']
            if category_name == 'custom':
                # Get custom categories
                custom_items = self.search([
                    ('user_id', '=', user_id),
                    ('category', '=', 'custom'),
                    ('is_active', '=', True)
                ])
                custom_categories = {}
                for item in custom_items:
                    if item.custom_category:
                        if item.custom_category not in custom_categories:
                            custom_categories[item.custom_category] = 0
                        custom_categories[item.custom_category] += 1
                
                for custom_name, count in custom_categories.items():
                    result.append({
                        'category': 'custom',
                        'display_name': custom_name,
                        'count': count
                    })
            else:
                result.append({
                    'category': category_name,
                    'display_name': dict(self._fields['category'].selection).get(category_name, category_name),
                    'count': cat['category_count']
                })
        
        return result
    
    @api.model
    def check_price_alerts(self):
        """Check and trigger price alerts for all users"""
        alerts_triggered = []
        
        # Get all active watchlist items with price alerts enabled
        watchlist_items = self.search([
            ('price_alert_enabled', '=', True),
            ('is_active', '=', True),
            '|',
            ('price_alert_above', '>', 0),
            ('price_alert_below', '>', 0)
        ])
        
        for item in watchlist_items:
            current_price = item.current_price
            
            # Check alert above
            if (item.price_alert_above and current_price and 
                current_price >= item.price_alert_above):
                alerts_triggered.append({
                    'user_id': item.user_id.id,
                    'symbol': item.symbol_id.symbol,
                    'type': 'above',
                    'price': current_price,
                    'threshold': item.price_alert_above
                })
            
            # Check alert below
            if (item.price_alert_below and current_price and 
                current_price <= item.price_alert_below):
                alerts_triggered.append({
                    'user_id': item.user_id.id,
                    'symbol': item.symbol_id.symbol,
                    'type': 'below',
                    'price': current_price,
                    'threshold': item.price_alert_below
                })
        
        # Send notifications for triggered alerts
        for alert in alerts_triggered:
            self._send_price_alert_notification(alert)
        
        return alerts_triggered
    
    def _send_price_alert_notification(self, alert):
        """Send price alert notification to user"""
        # This would integrate with Odoo's notification system
        # For now, just log the alert
        _logger.info(f"Price alert triggered for user {alert['user_id']}: "
                    f"{alert['symbol']} price {alert['price']} is {alert['type']} threshold {alert['threshold']}")
    
    def get_watchlist_summary(self):
        """Get watchlist summary for dashboard"""
        self.ensure_one()
        
        return {
            'symbol': self.symbol_id.symbol,
            'name': self.symbol_id.name,
            'current_price': self.current_price,
            'daily_change': self.daily_change,
            'daily_change_percent': self.daily_change_percent,
            'performance_since_added': self.performance_since_added,
            'target_price': self.target_price,
            'stop_loss': self.stop_loss,
            'alerts_enabled': self.price_alert_enabled,
            'category': self.category,
            'added_date': self.added_date,
        }
