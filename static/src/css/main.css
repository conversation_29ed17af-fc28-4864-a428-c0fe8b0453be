/* Trading<PERSON>iew <PERSON> Main Styles */

/* Global Styles */
:root {
    --primary-color: #2962ff;
    --success-color: #4caf50;
    --danger-color: #f44336;
    --warning-color: #ff9800;
    --info-color: #2196f3;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-color: #dee2e6;
    --text-muted: #6c757d;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
}

/* Header Styles */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

#header-search-form {
    position: relative;
}

#header-search {
    min-width: 300px;
    border-radius: 20px;
}

#search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    background: white;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

#search-results .dropdown-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
}

#search-results .dropdown-item:last-child {
    border-bottom: none;
}

#search-results .dropdown-item:hover {
    background-color: var(--light-color);
}

/* Market Status Indicator */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.status-open {
    background-color: var(--success-color);
    animation: pulse 2s infinite;
}

.status-closed {
    background-color: var(--text-muted);
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
    }
}

/* Symbol Cards */
.symbol-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border: 1px solid var(--border-color);
}

.symbol-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Table Styles */
.table th {
    border-top: none;
    font-weight: 600;
    color: var(--dark-color);
    background-color: var(--light-color);
}

.clickable-row:hover {
    background-color: var(--light-color);
}

.table-responsive {
    border-radius: 0.375rem;
    overflow: hidden;
}

/* Chart Container */
#tradingview-chart {
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    overflow: hidden;
}

/* Price Change Colors */
.text-success {
    color: var(--success-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.badge-success {
    background-color: var(--success-color);
}

.badge-danger {
    background-color: var(--danger-color);
}

/* Loading Skeleton */
.loading-skeleton {
    animation: pulse 1.5s ease-in-out infinite;
}

.skeleton-line {
    height: 1rem;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 0.25rem;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* News Cards */
.news-article-card {
    transition: transform 0.2s ease-in-out;
}

.news-article-card:hover {
    transform: translateY(-1px);
}

.news-title {
    color: var(--dark-color);
    font-weight: 600;
}

.news-title:hover {
    color: var(--primary-color);
    text-decoration: none;
}

/* Technical Indicators */
.technical-indicator-card {
    text-align: center;
    border: 1px solid var(--border-color);
}

.indicator-value {
    font-weight: 700;
    color: var(--dark-color);
}

.indicator-signal-bar {
    transition: width 0.3s ease-in-out;
}

/* Event Cards */
.event-card {
    border-left: 4px solid var(--primary-color);
}

.event-card.high-impact {
    border-left-color: var(--warning-color);
}

.event-card.critical-impact {
    border-left-color: var(--danger-color);
}

/* Filters Sidebar */
.filters-sidebar {
    background-color: var(--light-color);
    border-radius: 0.375rem;
    padding: 1.5rem;
}

.filter-group {
    margin-bottom: 1.5rem;
}

.filter-group:last-child {
    margin-bottom: 0;
}

/* Pagination */
.pagination .page-link {
    color: var(--primary-color);
    border-color: var(--border-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.pagination .page-link:hover {
    color: var(--primary-color);
    background-color: var(--light-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    #header-search {
        min-width: 200px;
    }
    
    .symbol-card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    #tradingview-chart {
        height: 300px !important;
    }
}

@media (max-width: 576px) {
    #header-search {
        min-width: 150px;
    }
    
    .navbar-nav .form-inline {
        margin-top: 0.5rem;
        margin-bottom: 0.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* Utility Classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.border-left-primary {
    border-left: 4px solid var(--primary-color) !important;
}

.border-left-success {
    border-left: 4px solid var(--success-color) !important;
}

.border-left-danger {
    border-left: 4px solid var(--danger-color) !important;
}

.border-left-warning {
    border-left: 4px solid var(--warning-color) !important;
}

/* Custom Scrollbar */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: var(--text-muted) var(--light-color);
}

.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: var(--light-color);
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: var(--text-muted);
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: var(--dark-color);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Price Flash Animation */
.price-flash {
    animation: priceFlash 1s ease-in-out;
}

@keyframes priceFlash {
    0% {
        background-color: transparent;
    }
    50% {
        background-color: #fff3cd;
    }
    100% {
        background-color: transparent;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #343a40;
        --dark-color: #f8f9fa;
        --border-color: #495057;
        --text-muted: #adb5bd;
    }
    
    body {
        background-color: #212529;
        color: var(--dark-color);
    }
    
    .card {
        background-color: var(--light-color);
        border-color: var(--border-color);
    }
    
    .table {
        color: var(--dark-color);
    }
    
    .table th {
        background-color: #495057;
        color: var(--dark-color);
    }
    
    #search-results {
        background-color: var(--light-color);
        border-color: var(--border-color);
    }
    
    #search-results .dropdown-item:hover {
        background-color: #495057;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .filters-sidebar,
    .pagination,
    .btn,
    .alert {
        display: none !important;
    }
    
    .container-fluid {
        padding: 0;
    }
    
    .card {
        border: 1px solid #000;
        break-inside: avoid;
    }
    
    .table {
        font-size: 0.75rem;
    }
}
