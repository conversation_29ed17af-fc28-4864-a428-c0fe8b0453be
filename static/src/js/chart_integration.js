/**
 * TradingView Chart Integration
 * Handles chart initialization and real-time updates
 */

class TradingViewChart {
    constructor(containerId, symbol, slug) {
        this.containerId = containerId;
        this.symbol = symbol;
        this.slug = slug;
        this.chart = null;
        this.candlestickSeries = null;
        this.volumeSeries = null;
        this.currentTimeframe = '1d';
        this.indicators = {};
        this.isLoading = false;
        
        this.init();
    }
    
    init() {
        // Initialize TradingView Lightweight Charts
        this.chart = LightweightCharts.createChart(document.getElementById(this.containerId), {
            width: document.getElementById(this.containerId).clientWidth,
            height: 400,
            layout: {
                backgroundColor: '#ffffff',
                textColor: '#333333',
            },
            grid: {
                vertLines: {
                    color: '#f0f0f0',
                },
                horzLines: {
                    color: '#f0f0f0',
                },
            },
            crosshair: {
                mode: LightweightCharts.CrosshairMode.Normal,
            },
            rightPriceScale: {
                borderColor: '#cccccc',
            },
            timeScale: {
                borderColor: '#cccccc',
                timeVisible: true,
                secondsVisible: false,
            },
        });
        
        // Add candlestick series
        this.candlestickSeries = this.chart.addCandlestickSeries({
            upColor: '#26a69a',
            downColor: '#ef5350',
            borderVisible: false,
            wickUpColor: '#26a69a',
            wickDownColor: '#ef5350',
        });
        
        // Add volume series
        this.volumeSeries = this.chart.addHistogramSeries({
            color: '#26a69a',
            priceFormat: {
                type: 'volume',
            },
            priceScaleId: 'volume',
            scaleMargins: {
                top: 0.8,
                bottom: 0,
            },
        });
        
        // Handle window resize
        window.addEventListener('resize', () => {
            this.chart.applyOptions({
                width: document.getElementById(this.containerId).clientWidth,
            });
        });
        
        // Load initial data
        this.loadChartData();
        
        // Set up timeframe buttons
        this.setupTimeframeButtons();
    }
    
    setupTimeframeButtons() {
        const buttons = document.querySelectorAll('[data-timeframe]');
        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const timeframe = button.getAttribute('data-timeframe');
                this.changeTimeframe(timeframe);
                
                // Update active button
                buttons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
            });
        });
    }
    
    async loadChartData(timeframe = '1d', limit = 100) {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading();
        
        try {
            const response = await fetch(`/market/${this.slug}/chart-data`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    jsonrpc: '2.0',
                    method: 'call',
                    params: {
                        timeframe: timeframe,
                        limit: limit
                    }
                })
            });
            
            const result = await response.json();
            
            if (result.error) {
                throw new Error(result.error);
            }
            
            const data = result.result;
            this.updateChart(data.data);
            
        } catch (error) {
            console.error('Error loading chart data:', error);
            this.showError('Failed to load chart data');
        } finally {
            this.isLoading = false;
            this.hideLoading();
        }
    }
    
    updateChart(data) {
        if (!data || data.length === 0) {
            this.showError('No chart data available');
            return;
        }
        
        // Prepare candlestick data
        const candlestickData = data.map(item => ({
            time: item.time,
            open: item.open,
            high: item.high,
            low: item.low,
            close: item.close,
        }));
        
        // Prepare volume data
        const volumeData = data.map(item => ({
            time: item.time,
            value: item.volume,
            color: item.close >= item.open ? '#26a69a' : '#ef5350',
        }));
        
        // Update series
        this.candlestickSeries.setData(candlestickData);
        this.volumeSeries.setData(volumeData);
        
        // Fit content
        this.chart.timeScale().fitContent();
    }
    
    changeTimeframe(timeframe) {
        this.currentTimeframe = timeframe;
        this.loadChartData(timeframe);
    }
    
    addIndicator(type, params = {}) {
        // This would add technical indicators to the chart
        // Implementation depends on the specific indicator
        switch (type) {
            case 'sma':
                this.addSMA(params.period || 20, params.color || '#2196F3');
                break;
            case 'ema':
                this.addEMA(params.period || 20, params.color || '#FF9800');
                break;
            case 'bollinger':
                this.addBollingerBands(params.period || 20, params.stdDev || 2);
                break;
        }
    }
    
    addSMA(period, color) {
        // Simple Moving Average implementation
        const smaData = this.calculateSMA(period);
        const smaSeries = this.chart.addLineSeries({
            color: color,
            lineWidth: 2,
            title: `SMA ${period}`,
        });
        smaSeries.setData(smaData);
        this.indicators[`sma_${period}`] = smaSeries;
    }
    
    addEMA(period, color) {
        // Exponential Moving Average implementation
        const emaData = this.calculateEMA(period);
        const emaSeries = this.chart.addLineSeries({
            color: color,
            lineWidth: 2,
            title: `EMA ${period}`,
        });
        emaSeries.setData(emaData);
        this.indicators[`ema_${period}`] = emaSeries;
    }
    
    addBollingerBands(period, stdDev) {
        // Bollinger Bands implementation
        const bbData = this.calculateBollingerBands(period, stdDev);
        
        const upperBand = this.chart.addLineSeries({
            color: '#9C27B0',
            lineWidth: 1,
            title: 'BB Upper',
        });
        
        const lowerBand = this.chart.addLineSeries({
            color: '#9C27B0',
            lineWidth: 1,
            title: 'BB Lower',
        });
        
        upperBand.setData(bbData.upper);
        lowerBand.setData(bbData.lower);
        
        this.indicators.bb_upper = upperBand;
        this.indicators.bb_lower = lowerBand;
    }
    
    calculateSMA(period) {
        // Simple Moving Average calculation
        const data = this.candlestickSeries.data();
        const smaData = [];
        
        for (let i = period - 1; i < data.length; i++) {
            let sum = 0;
            for (let j = 0; j < period; j++) {
                sum += data[i - j].close;
            }
            smaData.push({
                time: data[i].time,
                value: sum / period
            });
        }
        
        return smaData;
    }
    
    calculateEMA(period) {
        // Exponential Moving Average calculation
        const data = this.candlestickSeries.data();
        const emaData = [];
        const multiplier = 2 / (period + 1);
        
        // Start with SMA for first value
        let ema = data.slice(0, period).reduce((sum, item) => sum + item.close, 0) / period;
        emaData.push({
            time: data[period - 1].time,
            value: ema
        });
        
        // Calculate EMA for remaining values
        for (let i = period; i < data.length; i++) {
            ema = (data[i].close - ema) * multiplier + ema;
            emaData.push({
                time: data[i].time,
                value: ema
            });
        }
        
        return emaData;
    }
    
    calculateBollingerBands(period, stdDev) {
        // Bollinger Bands calculation
        const data = this.candlestickSeries.data();
        const upperBand = [];
        const lowerBand = [];
        
        for (let i = period - 1; i < data.length; i++) {
            const slice = data.slice(i - period + 1, i + 1);
            const sma = slice.reduce((sum, item) => sum + item.close, 0) / period;
            
            const variance = slice.reduce((sum, item) => {
                return sum + Math.pow(item.close - sma, 2);
            }, 0) / period;
            
            const standardDeviation = Math.sqrt(variance);
            
            upperBand.push({
                time: data[i].time,
                value: sma + (standardDeviation * stdDev)
            });
            
            lowerBand.push({
                time: data[i].time,
                value: sma - (standardDeviation * stdDev)
            });
        }
        
        return { upper: upperBand, lower: lowerBand };
    }
    
    removeIndicator(key) {
        if (this.indicators[key]) {
            this.chart.removeSeries(this.indicators[key]);
            delete this.indicators[key];
        }
    }
    
    showLoading() {
        const container = document.getElementById(this.containerId);
        const loadingDiv = document.createElement('div');
        loadingDiv.id = 'chart-loading';
        loadingDiv.className = 'd-flex justify-content-center align-items-center position-absolute w-100 h-100';
        loadingDiv.style.top = '0';
        loadingDiv.style.left = '0';
        loadingDiv.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
        loadingDiv.style.zIndex = '1000';
        loadingDiv.innerHTML = `
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Loading chart...</span>
            </div>
        `;
        container.style.position = 'relative';
        container.appendChild(loadingDiv);
    }
    
    hideLoading() {
        const loadingDiv = document.getElementById('chart-loading');
        if (loadingDiv) {
            loadingDiv.remove();
        }
    }
    
    showError(message) {
        const container = document.getElementById(this.containerId);
        container.innerHTML = `
            <div class="d-flex justify-content-center align-items-center h-100">
                <div class="text-center">
                    <i class="fa fa-exclamation-triangle text-warning fa-3x mb-3"></i>
                    <p class="text-muted">${message}</p>
                    <button class="btn btn-outline-primary btn-sm" onclick="location.reload()">
                        <i class="fa fa-refresh"></i> Retry
                    </button>
                </div>
            </div>
        `;
    }
    
    destroy() {
        if (this.chart) {
            this.chart.remove();
        }
    }
}

// Global chart instance
let tradingViewChart = null;

// Initialize chart function
function initTradingViewChart(symbol, slug) {
    // Load TradingView Lightweight Charts library if not already loaded
    if (typeof LightweightCharts === 'undefined') {
        const script = document.createElement('script');
        script.src = 'https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js';
        script.onload = () => {
            tradingViewChart = new TradingViewChart('tradingview-chart', symbol, slug);
        };
        document.head.appendChild(script);
    } else {
        tradingViewChart = new TradingViewChart('tradingview-chart', symbol, slug);
    }
}

// Real-time updates (WebSocket or polling)
function startRealTimeUpdates(symbol) {
    // This would implement real-time price updates
    // For now, we'll use polling every 30 seconds
    setInterval(async () => {
        if (tradingViewChart && !tradingViewChart.isLoading) {
            try {
                const response = await fetch(`/market/api/symbol/${symbol}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        jsonrpc: '2.0',
                        method: 'call',
                        params: {}
                    })
                });
                
                const result = await response.json();
                if (result.result && result.result.symbol) {
                    updatePriceDisplay(result.result.symbol);
                }
            } catch (error) {
                console.error('Error updating real-time data:', error);
            }
        }
    }, 30000); // Update every 30 seconds
}

// Update price display in header
function updatePriceDisplay(symbolData) {
    const priceElement = document.querySelector('.current-price');
    const changeElement = document.querySelector('.daily-change');
    
    if (priceElement && symbolData.current_price) {
        priceElement.textContent = symbolData.current_price.toFixed(2);
    }
    
    if (changeElement && symbolData.daily_change !== undefined) {
        const changePercent = symbolData.daily_change_percent || 0;
        changeElement.textContent = `${symbolData.daily_change.toFixed(2)} (${changePercent.toFixed(2)}%)`;
        changeElement.className = `badge badge-lg ${symbolData.daily_change >= 0 ? 'badge-success' : 'badge-danger'}`;
    }
}
