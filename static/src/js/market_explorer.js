/**
 * Market Explorer JavaScript functionality
 */

// Market Explorer functionality
function initMarketExplorer() {
    // Initialize real-time price updates
    startPriceUpdates();
    
    // Initialize filter form auto-submit
    initFilterAutoSubmit();
    
    // Initialize sorting
    initSorting();
}

function initViewSwitching() {
    const tableViewBtn = document.getElementById('table-view-btn');
    const cardViewBtn = document.getElementById('card-view-btn');
    const tableView = document.getElementById('table-view');
    const cardView = document.getElementById('card-view');
    
    if (tableViewBtn && cardViewBtn && tableView && cardView) {
        tableViewBtn.addEventListener('click', () => {
            tableView.classList.remove('d-none');
            cardView.classList.add('d-none');
            tableViewBtn.classList.add('active');
            cardViewBtn.classList.remove('active');
            localStorage.setItem('market-view', 'table');
        });
        
        cardViewBtn.addEventListener('click', () => {
            cardView.classList.remove('d-none');
            tableView.classList.add('d-none');
            cardViewBtn.classList.add('active');
            tableViewBtn.classList.remove('active');
            localStorage.setItem('market-view', 'card');
        });
        
        // Restore saved view preference
        const savedView = localStorage.getItem('market-view');
        if (savedView === 'card') {
            cardViewBtn.click();
        }
    }
}

function initClickableRows() {
    const clickableRows = document.querySelectorAll('.clickable-row');
    const symbolCards = document.querySelectorAll('.symbol-card');
    
    clickableRows.forEach(row => {
        row.addEventListener('click', function(e) {
            if (e.target.tagName !== 'A') {
                const href = this.getAttribute('data-href');
                if (href) {
                    window.location.href = href;
                }
            }
        });
        
        row.style.cursor = 'pointer';
    });
    
    symbolCards.forEach(card => {
        card.addEventListener('click', function(e) {
            if (e.target.tagName !== 'A') {
                const href = this.getAttribute('data-href');
                if (href) {
                    window.location.href = href;
                }
            }
        });
        
        card.style.cursor = 'pointer';
    });
}

function initFilterAutoSubmit() {
    const filterForm = document.getElementById('filter-form');
    if (!filterForm) return;
    
    const filterInputs = filterForm.querySelectorAll('select, input');
    
    filterInputs.forEach(input => {
        if (input.type === 'text') {
            // Debounce text inputs
            let timeout;
            input.addEventListener('input', () => {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    filterForm.submit();
                }, 1000);
            });
        } else {
            // Auto-submit for selects
            input.addEventListener('change', () => {
                filterForm.submit();
            });
        }
    });
}

function initSorting() {
    const sortableHeaders = document.querySelectorAll('th[data-sort]');
    
    sortableHeaders.forEach(header => {
        header.style.cursor = 'pointer';
        header.addEventListener('click', () => {
            const sortField = header.getAttribute('data-sort');
            const currentSort = new URLSearchParams(window.location.search).get('sort_by');
            const currentOrder = new URLSearchParams(window.location.search).get('sort_order');
            
            let newOrder = 'asc';
            if (currentSort === sortField && currentOrder === 'asc') {
                newOrder = 'desc';
            }
            
            const url = new URL(window.location);
            url.searchParams.set('sort_by', sortField);
            url.searchParams.set('sort_order', newOrder);
            url.searchParams.set('page', '1'); // Reset to first page
            
            window.location.href = url.toString();
        });
    });
}

function startPriceUpdates() {
    // Update prices every 30 seconds
    setInterval(updateMarketPrices, 30000);
}

async function updateMarketPrices() {
    try {
        // Get visible symbols
        const symbolElements = document.querySelectorAll('[data-symbol]');
        const symbols = Array.from(symbolElements).map(el => el.getAttribute('data-symbol'));
        
        if (symbols.length === 0) return;
        
        const response = await fetch('/api/v1/symbols/bulk', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                jsonrpc: '2.0',
                method: 'call',
                params: {
                    operation: 'get',
                    symbol_list: symbols
                }
            })
        });
        
        const result = await response.json();
        
        if (result.result && result.result.symbols) {
            updatePriceDisplay(result.result.symbols);
        }
    } catch (error) {
        console.error('Error updating market prices:', error);
    }
}

function updatePriceDisplay(symbols) {
    symbols.forEach(symbol => {
        updateSymbolRow(symbol);
        updateSymbolCard(symbol);
    });
}

function updateSymbolRow(symbol) {
    const row = document.querySelector(`tr[data-symbol="${symbol.symbol}"]`);
    if (!row) return;
    
    // Update price
    const priceCell = row.querySelector('.price-cell');
    if (priceCell && symbol.current_price) {
        priceCell.textContent = symbol.current_price.toFixed(2);
        flashCell(priceCell);
    }
    
    // Update change
    const changeCell = row.querySelector('.change-cell');
    if (changeCell && symbol.daily_change !== undefined) {
        changeCell.textContent = symbol.daily_change.toFixed(2);
        changeCell.className = `change-cell ${symbol.daily_change >= 0 ? 'text-success' : 'text-danger'}`;
        flashCell(changeCell);
    }
    
    // Update change percent
    const changePercentCell = row.querySelector('.change-percent-cell');
    if (changePercentCell && symbol.daily_change_percent !== undefined) {
        changePercentCell.textContent = symbol.daily_change_percent.toFixed(2) + '%';
        changePercentCell.className = `change-percent-cell ${symbol.daily_change_percent >= 0 ? 'text-success' : 'text-danger'}`;
        flashCell(changePercentCell);
    }
    
    // Update volume
    const volumeCell = row.querySelector('.volume-cell');
    if (volumeCell && symbol.volume) {
        volumeCell.textContent = formatNumber(symbol.volume);
        flashCell(volumeCell);
    }
}

function updateSymbolCard(symbol) {
    const card = document.querySelector(`.symbol-card[data-symbol="${symbol.symbol}"]`);
    if (!card) return;
    
    // Update price
    const priceElement = card.querySelector('.card-price');
    if (priceElement && symbol.current_price) {
        priceElement.textContent = symbol.current_price.toFixed(2);
        flashElement(priceElement);
    }
    
    // Update change
    const changeElement = card.querySelector('.card-change');
    if (changeElement && symbol.daily_change !== undefined) {
        changeElement.textContent = `${symbol.daily_change.toFixed(2)} (${symbol.daily_change_percent.toFixed(2)}%)`;
        changeElement.className = `card-change ${symbol.daily_change >= 0 ? 'text-success' : 'text-danger'}`;
        flashElement(changeElement);
    }
}

function flashCell(element) {
    element.style.backgroundColor = '#fff3cd';
    setTimeout(() => {
        element.style.backgroundColor = '';
    }, 1000);
}

function flashElement(element) {
    element.style.backgroundColor = '#fff3cd';
    setTimeout(() => {
        element.style.backgroundColor = '';
    }, 1000);
}

function formatNumber(num) {
    if (num >= 1e9) {
        return (num / 1e9).toFixed(1) + 'B';
    } else if (num >= 1e6) {
        return (num / 1e6).toFixed(1) + 'M';
    } else if (num >= 1e3) {
        return (num / 1e3).toFixed(1) + 'K';
    }
    return num.toString();
}

// Advanced filtering
function initAdvancedFilters() {
    const advancedToggle = document.getElementById('advanced-filters-toggle');
    const advancedFilters = document.getElementById('advanced-filters');
    
    if (advancedToggle && advancedFilters) {
        advancedToggle.addEventListener('click', () => {
            advancedFilters.classList.toggle('d-none');
            const isVisible = !advancedFilters.classList.contains('d-none');
            advancedToggle.textContent = isVisible ? 'Hide Advanced Filters' : 'Show Advanced Filters';
        });
    }
}

// Export functionality
function exportSymbolList() {
    const symbols = Array.from(document.querySelectorAll('[data-symbol]')).map(el => el.getAttribute('data-symbol'));
    const csvContent = generateCSV(symbols);
    downloadCSV(csvContent, 'symbols.csv');
}

function generateCSV(symbols) {
    let csv = 'Symbol,Name,Type,Exchange,Price,Change,Change%,Volume\n';
    
    symbols.forEach(symbol => {
        const row = document.querySelector(`tr[data-symbol="${symbol}"]`);
        if (row) {
            const cells = row.querySelectorAll('td');
            const rowData = Array.from(cells).map(cell => cell.textContent.trim()).join(',');
            csv += rowData + '\n';
        }
    });
    
    return csv;
}

function downloadCSV(content, filename) {
    const blob = new Blob([content], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

// Watchlist bulk operations
function initBulkWatchlistOperations() {
    const selectAllCheckbox = document.getElementById('select-all-symbols');
    const symbolCheckboxes = document.querySelectorAll('.symbol-checkbox');
    const bulkAddBtn = document.getElementById('bulk-add-to-watchlist');
    
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', (e) => {
            symbolCheckboxes.forEach(checkbox => {
                checkbox.checked = e.target.checked;
            });
            updateBulkButtons();
        });
    }
    
    symbolCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkButtons);
    });
    
    if (bulkAddBtn) {
        bulkAddBtn.addEventListener('click', bulkAddToWatchlist);
    }
}

function updateBulkButtons() {
    const selectedSymbols = document.querySelectorAll('.symbol-checkbox:checked');
    const bulkButtons = document.querySelectorAll('.bulk-action-btn');
    
    bulkButtons.forEach(btn => {
        btn.disabled = selectedSymbols.length === 0;
        btn.textContent = `${btn.dataset.action} (${selectedSymbols.length})`;
    });
}

async function bulkAddToWatchlist() {
    const selectedSymbols = Array.from(document.querySelectorAll('.symbol-checkbox:checked'))
        .map(checkbox => checkbox.value);
    
    if (selectedSymbols.length === 0) return;
    
    try {
        const response = await fetch('/api/v1/symbols/bulk', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                jsonrpc: '2.0',
                method: 'call',
                params: {
                    operation: 'add_to_watchlist',
                    symbol_list: selectedSymbols,
                    category: 'stocks'
                }
            })
        });
        
        const result = await response.json();
        
        if (result.result) {
            showSuccessMessage(`Added ${result.result.added_count} symbols to watchlist`);
            
            if (result.result.errors.length > 0) {
                console.warn('Some symbols failed to add:', result.result.errors);
            }
        }
    } catch (error) {
        console.error('Error bulk adding to watchlist:', error);
        showErrorMessage('Failed to add symbols to watchlist');
    }
}

// Keyboard shortcuts
function initKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.getElementById('header-search');
            if (searchInput) {
                searchInput.focus();
            }
        }
        
        // Escape to close search results
        if (e.key === 'Escape') {
            hideSearchResults();
        }
    });
}
