# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import UserError
from unittest.mock import patch, MagicMock
import json


class TestTradingViewAPIService(TransactionCase):
    
    def setUp(self):
        super().setUp()
        
        # Create API service instance
        self.api_service = self.env['tradingview.api.service']
        
        # Create test symbol
        self.test_symbol = self.env['tradingview.symbol'].create({
            'symbol': 'TESTBTC',
            'name': 'Test Bitcoin',
            'type': 'crypto',
            'exchange': 'TEST',
            'region': 'Global',
            'currency': 'USD',
            'active': True,
        })
        
        # Mock API configurations
        self.env['ir.config_parameter'].sudo().set_param('tradingview.twelvedata_api_key', 'test_key')
        self.env['ir.config_parameter'].sudo().set_param('tradingview.newsapi_key', 'test_news_key')
    
    def test_twelvedata_config(self):
        """Test TwelveData API configuration"""
        config = self.api_service._get_twelvedata_config()
        
        self.assertEqual(config['api_key'], 'test_key')
        self.assertEqual(config['base_url'], 'https://api.twelvedata.com')
        self.assertIn('rate_limit', config)
        self.assertIn('timeout', config)
    
    def test_binance_config(self):
        """Test Binance API configuration"""
        config = self.api_service._get_binance_config()
        
        self.assertEqual(config['base_url'], 'https://api.binance.com/api/v3')
        self.assertIn('rate_limit', config)
        self.assertIn('timeout', config)
    
    def test_newsapi_config(self):
        """Test NewsAPI configuration"""
        config = self.api_service._get_newsapi_config()
        
        self.assertEqual(config['api_key'], 'test_news_key')
        self.assertEqual(config['base_url'], 'https://newsapi.org/v2')
        self.assertIn('rate_limit', config)
        self.assertIn('timeout', config)
    
    @patch('requests.Session.get')
    def test_make_api_request_success(self, mock_get):
        """Test successful API request"""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'data': 'test_data'}
        mock_get.return_value = mock_response
        
        result = self.api_service._make_api_request('https://test.com/api', {'param': 'value'})
        
        self.assertEqual(result, {'data': 'test_data'})
        mock_get.assert_called_once()
    
    @patch('requests.Session.get')
    def test_make_api_request_rate_limit(self, mock_get):
        """Test API request with rate limiting"""
        # Mock rate limit response
        mock_response = MagicMock()
        mock_response.status_code = 429
        mock_response.headers = {'Retry-After': '1'}
        mock_get.return_value = mock_response
        
        with patch('time.sleep') as mock_sleep:
            result = self.api_service._make_api_request('https://test.com/api', retries=1)
            
            self.assertIsNone(result)
            mock_sleep.assert_called_with(1)
    
    @patch('requests.Session.get')
    def test_make_api_request_retry(self, mock_get):
        """Test API request retry logic"""
        # Mock failed requests
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.raise_for_status.side_effect = Exception('Server Error')
        mock_get.return_value = mock_response
        
        with patch('time.sleep'):
            result = self.api_service._make_api_request('https://test.com/api', retries=2)
            
            self.assertIsNone(result)
            self.assertEqual(mock_get.call_count, 2)  # Should retry
    
    def test_parse_datetime_iso_format(self):
        """Test datetime parsing with ISO format"""
        iso_date = '2024-01-01T12:00:00Z'
        result = self.api_service._parse_datetime(iso_date)
        
        self.assertIsNotNone(result)
    
    def test_parse_datetime_invalid(self):
        """Test datetime parsing with invalid format"""
        invalid_date = 'invalid_date'
        result = self.api_service._parse_datetime(invalid_date)
        
        # Should return current datetime for invalid input
        self.assertIsNotNone(result)
    
    def test_bulk_create_symbols_success(self):
        """Test bulk symbol creation"""
        symbols_data = [
            {
                'symbol': 'BULK1',
                'name': 'Bulk Test 1',
                'type': 'stock',
                'exchange': 'TEST',
            },
            {
                'symbol': 'BULK2',
                'name': 'Bulk Test 2',
                'type': 'stock',
                'exchange': 'TEST',
            }
        ]
        
        result = self.api_service._bulk_create_symbols(symbols_data)
        
        self.assertEqual(result['success_count'], 2)
        self.assertEqual(result['error_count'], 0)
        
        # Verify symbols were created
        bulk1 = self.env['tradingview.symbol'].search([('symbol', '=', 'BULK1')])
        bulk2 = self.env['tradingview.symbol'].search([('symbol', '=', 'BULK2')])
        
        self.assertTrue(bulk1)
        self.assertTrue(bulk2)
    
    def test_bulk_create_symbols_duplicates(self):
        """Test bulk symbol creation with duplicates"""
        symbols_data = [
            {
                'symbol': 'TESTBTC',  # Already exists
                'name': 'Duplicate Test',
                'type': 'crypto',
                'exchange': 'TEST',
            },
            {
                'symbol': 'NEWBULK',
                'name': 'New Bulk Test',
                'type': 'stock',
                'exchange': 'TEST',
            }
        ]
        
        result = self.api_service._bulk_create_symbols(symbols_data)
        
        # Should skip duplicate and create new one
        self.assertEqual(result['success_count'], 1)
        self.assertEqual(result['error_count'], 0)
    
    def test_bulk_create_symbols_errors(self):
        """Test bulk symbol creation with errors"""
        symbols_data = [
            {
                'symbol': '',  # Invalid empty symbol
                'name': 'Invalid Test',
                'type': 'stock',
                'exchange': 'TEST',
            },
            {
                'symbol': 'VALID',
                'name': 'Valid Test',
                'type': 'stock',
                'exchange': 'TEST',
            }
        ]
        
        result = self.api_service._bulk_create_symbols(symbols_data)
        
        # Should handle errors gracefully
        self.assertEqual(result['success_count'], 1)
        self.assertEqual(result['error_count'], 1)
    
    @patch.object(api_service, '_make_api_request')
    def test_sync_symbols_twelvedata_no_api_key(self, mock_request):
        """Test TwelveData sync without API key"""
        # Remove API key
        self.env['ir.config_parameter'].sudo().set_param('tradingview.twelvedata_api_key', '')
        
        with self.assertRaises(UserError):
            self.api_service.sync_symbols_twelvedata(['stock'])
    
    @patch.object(api_service, '_make_api_request')
    def test_sync_crypto_data_binance_success(self, mock_request):
        """Test Binance crypto data sync"""
        # Mock Binance API response
        mock_request.return_value = [
            [1640995200000, "50000.0", "52000.0", "49000.0", "51000.0", "1000.0"]
        ]
        
        result = self.api_service.sync_crypto_data_binance(['BTCUSDT'])
        
        self.assertIsNotNone(result)
        self.assertEqual(result.api_name, 'binance')
        self.assertEqual(result.sync_type, 'ohlc')
    
    @patch.object(api_service, '_make_api_request')
    def test_sync_financial_news_no_api_key(self, mock_request):
        """Test NewsAPI sync without API key"""
        # Remove API key
        self.env['ir.config_parameter'].sudo().set_param('tradingview.newsapi_key', '')
        
        with self.assertRaises(UserError):
            self.api_service.sync_financial_news(['AAPL'])
    
    @patch.object(api_service, '_make_api_request')
    def test_sync_financial_news_success(self, mock_request):
        """Test successful news sync"""
        # Mock NewsAPI response
        mock_request.return_value = {
            'articles': [
                {
                    'title': 'Test News Article',
                    'description': 'Test description',
                    'url': 'https://example.com/news',
                    'source': {'name': 'Test Source'},
                    'author': 'Test Author',
                    'publishedAt': '2024-01-01T12:00:00Z'
                }
            ]
        }
        
        result = self.api_service.sync_financial_news(['TESTBTC'])
        
        self.assertIsNotNone(result)
        self.assertEqual(result.api_name, 'newsapi')
        self.assertEqual(result.sync_type, 'news')
    
    def test_sync_log_creation(self):
        """Test sync log creation and management"""
        # Test sync log creation
        sync_log = self.env['tradingview.sync_log'].create_sync_log(
            'test_api', 'symbols', triggered_by='manual'
        )
        
        self.assertEqual(sync_log.api_name, 'test_api')
        self.assertEqual(sync_log.sync_type, 'symbols')
        self.assertEqual(sync_log.status, 'running')
        self.assertEqual(sync_log.triggered_by, 'manual')
        
        # Test progress update
        sync_log.update_progress(
            api_calls=5,
            processed=100,
            created=95,
            failed=5
        )
        
        self.assertEqual(sync_log.api_calls_made, 5)
        self.assertEqual(sync_log.records_processed, 100)
        self.assertEqual(sync_log.records_created, 95)
        self.assertEqual(sync_log.records_failed, 5)
        self.assertEqual(sync_log.success_rate, 95.0)
        
        # Test completion
        sync_log.mark_completed('success')
        
        self.assertEqual(sync_log.status, 'success')
        self.assertIsNotNone(sync_log.end_time)
        self.assertGreater(sync_log.duration_seconds, 0)
    
    def test_sync_log_failure(self):
        """Test sync log failure handling"""
        sync_log = self.env['tradingview.sync_log'].create_sync_log(
            'test_api', 'ohlc', triggered_by='cron'
        )
        
        # Test failure
        sync_log.mark_failed('Test error message', 'Detailed error info')
        
        self.assertEqual(sync_log.status, 'failure')
        self.assertEqual(sync_log.error_message, 'Test error message')
        self.assertEqual(sync_log.error_details, 'Detailed error info')
        self.assertIsNotNone(sync_log.end_time)
    
    def test_sync_statistics(self):
        """Test sync statistics calculation"""
        # Create multiple sync logs
        for i in range(5):
            sync_log = self.env['tradingview.sync_log'].create({
                'api_name': 'test_api',
                'sync_type': 'symbols',
                'status': 'success' if i < 4 else 'failure',
                'start_time': '2024-01-01 12:00:00',
                'end_time': '2024-01-01 12:01:00',
                'duration_seconds': 60,
                'records_processed': 100,
                'triggered_by': 'cron',
            })
        
        stats = self.env['tradingview.sync_log'].get_sync_statistics(days=7)
        
        self.assertIn('total_syncs', stats)
        self.assertIn('success_rate', stats)
        self.assertIn('avg_duration', stats)
        self.assertEqual(stats['total_syncs'], 5)
        self.assertEqual(stats['success_rate'], 80.0)  # 4/5 * 100
    
    def tearDown(self):
        """Clean up test data"""
        super().tearDown()
