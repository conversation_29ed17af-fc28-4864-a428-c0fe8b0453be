<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Event Tree View -->
        <record id="view_tradingview_event_tree" model="ir.ui.view">
            <field name="name">tradingview.event.tree</field>
            <field name="model">tradingview.event</field>
            <field name="arch" type="xml">
                <tree string="Financial Events" default_order="date asc">
                    <field name="date" string="Date"/>
                    <field name="symbol_id" string="Symbol"/>
                    <field name="title" string="Title"/>
                    <field name="event_type" string="Type"/>
                    <field name="impact_level" string="Impact" 
                           decoration-danger="impact_level == 'critical'" 
                           decoration-warning="impact_level == 'high'"
                           decoration-info="impact_level == 'medium'"/>
                    <field name="status" string="Status"/>
                    <field name="is_upcoming" string="Upcoming" widget="boolean_toggle"/>
                    <field name="time_until" string="Time Until"/>
                    <field name="location" string="Location"/>
                    <field name="estimated_eps" string="Est. EPS" invisible="event_type != 'earnings'"/>
                    <field name="actual_eps" string="Act. EPS" invisible="event_type != 'earnings'"/>
                </tree>
            </field>
        </record>
        
        <!-- Event Form View -->
        <record id="view_tradingview_event_form" model="ir.ui.view">
            <field name="name">tradingview.event.form</field>
            <field name="model">tradingview.event</field>
            <field name="arch" type="xml">
                <form string="Financial Event">
                    <header>
                        <button name="mark_completed" type="object" string="Mark Completed" class="btn-success"
                                invisible="status in ['completed', 'cancelled']"/>
                        <button name="mark_cancelled" type="object" string="Mark Cancelled" class="btn-warning"
                                invisible="status in ['completed', 'cancelled']"/>
                        <field name="status" widget="statusbar" statusbar_visible="scheduled,in_progress,completed"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="title" placeholder="Event Title"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group name="basic_info" string="Basic Information">
                                <field name="symbol_id"/>
                                <field name="event_type"/>
                                <field name="date"/>
                                <field name="impact_level"/>
                                <field name="location"/>
                                <field name="timezone"/>
                                <field name="duration_minutes"/>
                            </group>
                            <group name="timing" string="Timing">
                                <field name="is_upcoming"/>
                                <field name="is_today"/>
                                <field name="days_until"/>
                                <field name="time_until"/>
                                <field name="is_recurring"/>
                                <field name="recurrence_pattern" invisible="is_recurring == False"/>
                            </group>
                        </group>
                        
                        <group>
                            <field name="description" placeholder="Event description"/>
                        </group>
                        
                        <group>
                            <group name="links" string="Links">
                                <field name="link" widget="url"/>
                                <field name="webcast_link" widget="url"/>
                                <field name="document_link" widget="url"/>
                            </group>
                            <group name="metadata" string="Metadata">
                                <field name="source"/>
                                <field name="external_id"/>
                                <field name="created_date"/>
                            </group>
                        </group>
                        
                        <group string="Earnings Data" invisible="event_type != 'earnings'">
                            <group name="estimates" string="Estimates">
                                <field name="estimated_eps"/>
                                <field name="estimated_revenue"/>
                            </group>
                            <group name="actuals" string="Actuals">
                                <field name="actual_eps"/>
                                <field name="actual_revenue"/>
                            </group>
                        </group>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>
        
        <!-- Event Search View -->
        <record id="view_tradingview_event_search" model="ir.ui.view">
            <field name="name">tradingview.event.search</field>
            <field name="model">tradingview.event</field>
            <field name="arch" type="xml">
                <search string="Search Events">
                    <field name="title" string="Title" filter_domain="[('title', 'ilike', self)]"/>
                    <field name="symbol_id" string="Symbol"/>
                    <field name="event_type"/>
                    <field name="location"/>
                    
                    <filter string="Upcoming" name="upcoming" domain="[('is_upcoming', '=', True)]"/>
                    <filter string="Today" name="today" domain="[('is_today', '=', True)]"/>
                    <filter string="Scheduled" name="scheduled" domain="[('status', '=', 'scheduled')]"/>
                    <filter string="Completed" name="completed" domain="[('status', '=', 'completed')]"/>
                    
                    <separator/>
                    <filter string="This Week" name="this_week" 
                            domain="[('date', '>=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d')),
                                     ('date', '&lt;', (context_today() + datetime.timedelta(days=7-context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                    <filter string="This Month" name="this_month" 
                            domain="[('date', '>=', context_today().strftime('%Y-%m-01'))]"/>
                    <filter string="Next 30 Days" name="next_30_days" 
                            domain="[('date', '>=', context_today().strftime('%Y-%m-%d')),
                                     ('date', '&lt;', (context_today() + datetime.timedelta(days=30)).strftime('%Y-%m-%d'))]"/>
                    
                    <separator/>
                    <filter string="Critical Impact" name="critical" domain="[('impact_level', '=', 'critical')]"/>
                    <filter string="High Impact" name="high_impact" domain="[('impact_level', '=', 'high')]"/>
                    <filter string="Medium Impact" name="medium_impact" domain="[('impact_level', '=', 'medium')]"/>
                    
                    <separator/>
                    <filter string="Earnings" name="earnings" domain="[('event_type', '=', 'earnings')]"/>
                    <filter string="Dividends" name="dividends" domain="[('event_type', '=', 'dividend')]"/>
                    <filter string="Fed Meetings" name="fed_meetings" domain="[('event_type', '=', 'fed_meeting')]"/>
                    <filter string="IPOs" name="ipos" domain="[('event_type', '=', 'ipo')]"/>
                    
                    <group expand="0" string="Group By">
                        <filter string="Symbol" name="group_symbol" context="{'group_by': 'symbol_id'}"/>
                        <filter string="Event Type" name="group_type" context="{'group_by': 'event_type'}"/>
                        <filter string="Impact Level" name="group_impact" context="{'group_by': 'impact_level'}"/>
                        <filter string="Status" name="group_status" context="{'group_by': 'status'}"/>
                        <filter string="Date" name="group_date" context="{'group_by': 'date:day'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- Event Calendar View -->
        <record id="view_tradingview_event_calendar" model="ir.ui.view">
            <field name="name">tradingview.event.calendar</field>
            <field name="model">tradingview.event</field>
            <field name="arch" type="xml">
                <calendar string="Events Calendar" date_start="date" color="impact_level" mode="month">
                    <field name="title"/>
                    <field name="symbol_id"/>
                    <field name="event_type"/>
                    <field name="impact_level"/>
                </calendar>
            </field>
        </record>
        
        <!-- Event Kanban View -->
        <record id="view_tradingview_event_kanban" model="ir.ui.view">
            <field name="name">tradingview.event.kanban</field>
            <field name="model">tradingview.event</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile" default_group_by="event_type">
                    <field name="title"/>
                    <field name="symbol_id"/>
                    <field name="date"/>
                    <field name="event_type"/>
                    <field name="impact_level"/>
                    <field name="status"/>
                    <field name="time_until"/>
                    <field name="is_upcoming"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="title"/>
                                        </strong>
                                        <div class="o_kanban_record_subtitle">
                                            <field name="symbol_id"/> • <field name="date"/>
                                        </div>
                                    </div>
                                    <div class="o_kanban_record_body">
                                        <div class="row">
                                            <div class="col-6">
                                                <span t-attf-class="badge badge-pill #{record.impact_level.raw_value === 'critical' ? 'badge-danger' : record.impact_level.raw_value === 'high' ? 'badge-warning' : record.impact_level.raw_value === 'medium' ? 'badge-info' : 'badge-secondary'}">
                                                    <field name="impact_level"/>
                                                </span>
                                            </div>
                                            <div class="col-6 text-right">
                                                <span t-attf-class="badge badge-pill #{record.status.raw_value === 'completed' ? 'badge-success' : record.status.raw_value === 'cancelled' ? 'badge-danger' : record.status.raw_value === 'in_progress' ? 'badge-warning' : 'badge-secondary'}">
                                                    <field name="status"/>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-12">
                                                <t t-if="record.is_upcoming.raw_value">
                                                    <small class="text-success">
                                                        <i class="fa fa-clock-o"/> <field name="time_until"/>
                                                    </small>
                                                </t>
                                                <t t-else="">
                                                    <small class="text-muted">
                                                        <i class="fa fa-check"/> Past Event
                                                    </small>
                                                </t>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>
        
        <!-- Action -->
        <record id="action_tradingview_event" model="ir.actions.act_window">
            <field name="name">Financial Events</field>
            <field name="res_model">tradingview.event</field>
            <field name="view_mode">tree,kanban,calendar,form</field>
            <field name="context">{'search_default_upcoming': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No financial events found!
                </p>
                <p>
                    Financial events like earnings reports, dividend payments, Fed meetings, and IPOs will appear here once you start synchronizing event data.
                </p>
            </field>
        </record>
        
        <!-- Earnings Calendar Action -->
        <record id="action_tradingview_earnings_calendar" model="ir.actions.act_window">
            <field name="name">Earnings Calendar</field>
            <field name="res_model">tradingview.event</field>
            <field name="view_mode">calendar,tree,form</field>
            <field name="domain">[('event_type', '=', 'earnings')]</field>
            <field name="context">{'search_default_upcoming': 1, 'default_event_type': 'earnings'}</field>
        </record>
        
    </data>
</odoo>
