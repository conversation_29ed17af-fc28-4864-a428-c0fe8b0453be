<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Market Explorer Template -->
        <template id="market_explorer_template" name="Market Explorer">
            <t t-call="website.layout">
                <t t-set="title" t-value="'Market Explorer'"/>
                <t t-set="additional_title" t-value="'Financial Symbols &amp; Market Data'"/>
                
                <!-- <PERSON> Header -->
                <div class="container-fluid bg-primary text-white py-4">
                    <div class="container">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h1 class="mb-2">Market Explorer</h1>
                                <p class="mb-0">Discover and track financial symbols across all markets</p>
                            </div>
                            <div class="col-md-4 text-right">
                                <div class="d-flex flex-column align-items-end">
                                    <div class="h5 mb-1">
                                        <span t-esc="total_symbols"/> Symbols
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Main Content -->
                <div class="container-fluid">
                    <div class="row">
                        <!-- Main Content -->
                        <div class="col-12">
                            <!-- Symbols Table View -->
                            <div class="card mt-4">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="thead-light">
                                            <tr>
                                                <th>Symbol</th>
                                                <th>Name</th>
                                                <th>Type</th>
                                                <th>Exchange</th>
                                                <th class="text-right">Price</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr t-foreach="symbols" t-as="symbol">
                                                <td>
                                                    <a t-attf-href="/market/#{symbol.slug}" class="font-weight-bold text-decoration-none">
                                                        <span t-esc="symbol.symbol"/>
                                                    </a>
                                                </td>
                                                <td t-esc="symbol.name"/>
                                                <td>
                                                    <span class="badge badge-info" t-esc="symbol.type.title()"/>
                                                </td>
                                                <td t-esc="symbol.exchange"/>
                                                <td class="text-right">
                                                    <span t-if="symbol.current_price" t-esc="symbol.current_price"/>
                                                    <span t-else="">--</span>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>
        
    </data>
</odoo>
