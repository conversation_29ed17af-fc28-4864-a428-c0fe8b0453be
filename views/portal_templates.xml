<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Portal Layout Extension -->
        <template id="portal_layout_extend" inherit_id="portal.portal_layout" name="Portal Layout Extension">
            <xpath expr="//ol[hasclass('o_portal_submenu')]" position="inside">
                <li t-if="page_name == 'watchlist' or page_name == 'watchlist_detail' or page_name == 'watchlist_edit'">
                    <a t-attf-href="/my/watchlist?{{ keep_query() }}">My Watchlist</a>
                </li>
                <li t-if="page_name == 'alerts'">
                    <a href="/my/alerts"><PERSON> Alerts</a>
                </li>
                <li t-if="page_name == 'portfolio'">
                    <a href="/my/portfolio">Portfolio Overview</a>
                </li>
            </xpath>
        </template>
        
        <!-- Portal My Home Extension -->
        <template id="portal_my_home_extend" inherit_id="portal.portal_my_home" name="Portal My Home Extension">
            <xpath expr="//div[hasclass('o_portal_docs')]" position="inside">
                <t t-call="portal.portal_docs_entry">
                    <t t-set="title">Watchlist</t>
                    <t t-set="url" t-value="'/my/watchlist'"/>
                    <t t-set="placeholder_count" t-value="'watchlist_count'"/>
                </t>
            </xpath>
        </template>
        
        <!-- My Watchlist Template -->
        <template id="portal_my_watchlist" name="My Watchlist">
            <t t-call="portal.portal_layout">
                <t t-set="breadcrumbs_searchbar" t-value="True"/>
                
                <t t-call="portal.portal_searchbar">
                    <t t-set="title">My Watchlist</t>
                </t>
                
                <!-- Portfolio Summary -->
                <div class="row mb-4">
                    <div class="col-lg-12">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">Portfolio Summary</h5>
                                <div class="row">
                                    <div class="col-md-3 text-center">
                                        <h3 class="text-primary" t-esc="portfolio_stats['total_symbols']"/>
                                        <p class="mb-0">Total Symbols</p>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <h3 t-attf-class="#{portfolio_stats['total_change'] >= 0 and 'text-success' or 'text-danger'}">
                                            <span t-esc="portfolio_stats['total_change']" t-options="{'widget': 'monetary'}"/>
                                        </h3>
                                        <p class="mb-0">Total Change</p>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <h3 t-attf-class="#{portfolio_stats['total_change_percent'] >= 0 and 'text-success' or 'text-danger'}">
                                            <span t-esc="portfolio_stats['total_change_percent']" t-options="{'precision': 2}"/>%
                                        </h3>
                                        <p class="mb-0">Total Change %</p>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <h3 class="text-info">
                                            <span class="text-success" t-esc="portfolio_stats['positive_count']"/>/<span class="text-danger" t-esc="portfolio_stats['negative_count']"/>
                                        </h3>
                                        <p class="mb-0">Winners/Losers</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Categories Filter -->
                <div class="row mb-3">
                    <div class="col-lg-12">
                        <div class="btn-group" role="group">
                            <a t-attf-href="/my/watchlist?#{keep_query('category')}" 
                               t-attf-class="btn btn-outline-primary #{not category and 'active' or ''}">
                                All (<t t-esc="sum(cat['count'] for cat in categories)"/>)
                            </a>
                            <t t-foreach="categories" t-as="cat">
                                <a t-attf-href="/my/watchlist?category=#{cat['category']}&amp;#{keep_query('category')}" 
                                   t-attf-class="btn btn-outline-primary #{category == cat['category'] and 'active' or ''}">
                                    <t t-esc="cat['display_name']"/> (<t t-esc="cat['count']"/>)
                                </a>
                            </t>
                        </div>
                    </div>
                </div>
                
                <t t-if="not watchlist_items">
                    <div class="alert alert-info text-center" role="alert">
                        <h4 class="alert-heading">Your watchlist is empty</h4>
                        <p>Start building your watchlist by exploring symbols and adding them to track their performance.</p>
                        <a href="/market" class="btn btn-primary">Explore Symbols</a>
                    </div>
                </t>
                
                <t t-if="watchlist_items">
                    <!-- Watchlist Table -->
                    <div class="card">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Symbol</th>
                                        <th>Name</th>
                                        <th>Category</th>
                                        <th class="text-right">Current Price</th>
                                        <th class="text-right">Daily Change</th>
                                        <th class="text-right">Performance</th>
                                        <th class="text-right">Target Price</th>
                                        <th class="text-center">Alerts</th>
                                        <th class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr t-foreach="watchlist_items" t-as="item">
                                        <td>
                                            <a t-attf-href="/market/#{item.symbol_id.slug}" class="font-weight-bold text-decoration-none">
                                                <t t-esc="item.symbol_id.symbol"/>
                                            </a>
                                        </td>
                                        <td>
                                            <span t-esc="item.symbol_id.name"/>
                                            <br/>
                                            <small class="text-muted" t-esc="item.symbol_id.exchange"/>
                                        </td>
                                        <td>
                                            <span class="badge badge-info" t-esc="item.category.title()"/>
                                        </td>
                                        <td class="text-right">
                                            <span t-if="item.current_price" t-esc="item.current_price" t-options="{'widget': 'monetary', 'display_currency': item.symbol_id.currency}"/>
                                            <span t-else="">--</span>
                                        </td>
                                        <td class="text-right">
                                            <div t-if="item.daily_change_percent" 
                                                 t-attf-class="#{item.daily_change_percent >= 0 and 'text-success' or 'text-danger'}">
                                                <span t-esc="item.daily_change" t-options="{'widget': 'monetary', 'display_currency': item.symbol_id.currency}"/>
                                                (<span t-esc="item.daily_change_percent" t-options="{'precision': 2}"/>%)
                                            </div>
                                            <span t-else="">--</span>
                                        </td>
                                        <td class="text-right">
                                            <span t-if="item.performance_since_added" 
                                                  t-attf-class="#{item.performance_since_added >= 0 and 'text-success' or 'text-danger'}">
                                                <t t-esc="item.performance_since_added" t-options="{'precision': 2}"/>%
                                            </span>
                                            <span t-else="">--</span>
                                        </td>
                                        <td class="text-right">
                                            <span t-if="item.target_price" t-esc="item.target_price" t-options="{'widget': 'monetary', 'display_currency': item.symbol_id.currency}"/>
                                            <span t-else="">--</span>
                                        </td>
                                        <td class="text-center">
                                            <i t-if="item.price_alert_enabled" class="fa fa-bell text-warning" title="Price alert enabled"/>
                                            <i t-else="" class="fa fa-bell-slash text-muted" title="No alerts"/>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a t-attf-href="/my/watchlist/#{item.id}" class="btn btn-outline-primary btn-sm" title="View Details">
                                                    <i class="fa fa-eye"/>
                                                </a>
                                                <a t-attf-href="/my/watchlist/#{item.id}/edit" class="btn btn-outline-secondary btn-sm" title="Edit">
                                                    <i class="fa fa-edit"/>
                                                </a>
                                                <a t-attf-href="/my/watchlist/#{item.id}/remove" class="btn btn-outline-danger btn-sm" 
                                                   onclick="return confirm('Are you sure you want to remove this symbol from your watchlist?')" title="Remove">
                                                    <i class="fa fa-trash"/>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Pagination -->
                    <div t-if="pager" class="o_portal_pager text-center">
                        <t t-call="portal.pager"/>
                    </div>
                </t>
            </t>
        </template>
        
        <!-- Watchlist Detail Template -->
        <template id="portal_watchlist_detail" name="Watchlist Detail">
            <t t-call="portal.portal_layout">
                <t t-set="o_portal_fullwidth_alert" t-value="True"/>
                
                <div class="row">
                    <div class="col-lg-8">
                        <!-- Symbol Header -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h2 class="mb-1">
                                            <span class="font-weight-bold" t-esc="symbol.symbol"/>
                                            <small class="text-muted ml-2" t-esc="symbol.name"/>
                                        </h2>
                                        <div class="text-muted">
                                            <span class="badge badge-info mr-2" t-esc="symbol.type.title()"/>
                                            <span t-esc="symbol.exchange"/>
                                        </div>
                                    </div>
                                    <div class="col-md-4 text-right">
                                        <h3 class="mb-1">
                                            <span t-if="symbol.current_price" t-esc="symbol.current_price" t-options="{'widget': 'monetary', 'display_currency': symbol.currency}"/>
                                            <span t-else="">--</span>
                                        </h3>
                                        <div t-if="symbol.daily_change_percent" 
                                             t-attf-class="#{symbol.daily_change_percent >= 0 and 'text-success' or 'text-danger'}">
                                            <span t-esc="symbol.daily_change" t-options="{'widget': 'monetary', 'display_currency': symbol.currency}"/>
                                            (<span t-esc="symbol.daily_change_percent" t-options="{'precision': 2}"/>%)
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Chart -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Price Chart</h5>
                            </div>
                            <div class="card-body p-0">
                                <div id="watchlist-chart" style="height: 300px;">
                                    <div class="d-flex justify-content-center align-items-center h-100">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="sr-only">Loading chart...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Recent News -->
                        <div class="card" t-if="latest_news">
                            <div class="card-header">
                                <h5 class="mb-0">Recent News</h5>
                            </div>
                            <div class="card-body">
                                <div t-foreach="latest_news" t-as="news" class="mb-3">
                                    <h6>
                                        <a t-att-href="news.link" target="_blank" class="text-decoration-none" t-esc="news.title"/>
                                    </h6>
                                    <p class="text-muted mb-1" t-esc="news.display_summary"/>
                                    <small class="text-muted">
                                        <span t-esc="news.source"/> • <span t-esc="news.published_at" t-options="{'widget': 'datetime'}"/>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Sidebar -->
                    <div class="col-lg-4">
                        <!-- Watchlist Item Details -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">Watchlist Details</h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm">
                                    <tbody>
                                        <tr>
                                            <td>Added Date</td>
                                            <td class="text-right" t-esc="watchlist_item.added_date" t-options="{'widget': 'date'}"/>
                                        </tr>
                                        <tr>
                                            <td>Category</td>
                                            <td class="text-right">
                                                <span class="badge badge-info" t-esc="watchlist_item.category.title()"/>
                                            </td>
                                        </tr>
                                        <tr t-if="watchlist_item.price_when_added">
                                            <td>Price When Added</td>
                                            <td class="text-right" t-esc="watchlist_item.price_when_added" t-options="{'widget': 'monetary', 'display_currency': symbol.currency}"/>
                                        </tr>
                                        <tr t-if="watchlist_item.performance_since_added">
                                            <td>Performance</td>
                                            <td class="text-right" 
                                                t-attf-class="#{watchlist_item.performance_since_added >= 0 and 'text-success' or 'text-danger'}">
                                                <t t-esc="watchlist_item.performance_since_added" t-options="{'precision': 2}"/>%
                                            </td>
                                        </tr>
                                        <tr t-if="watchlist_item.target_price">
                                            <td>Target Price</td>
                                            <td class="text-right" t-esc="watchlist_item.target_price" t-options="{'widget': 'monetary', 'display_currency': symbol.currency}"/>
                                        </tr>
                                        <tr t-if="watchlist_item.stop_loss">
                                            <td>Stop Loss</td>
                                            <td class="text-right" t-esc="watchlist_item.stop_loss" t-options="{'widget': 'monetary', 'display_currency': symbol.currency}"/>
                                        </tr>
                                        <tr>
                                            <td>View Count</td>
                                            <td class="text-right" t-esc="watchlist_item.view_count"/>
                                        </tr>
                                    </tbody>
                                </table>
                                
                                <div class="mt-3">
                                    <a t-attf-href="/my/watchlist/#{watchlist_item.id}/edit" class="btn btn-primary btn-block">
                                        <i class="fa fa-edit"/> Edit Settings
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Price Alerts -->
                        <div class="card mb-4" t-if="watchlist_item.price_alert_enabled">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fa fa-bell text-warning"/> Price Alerts
                                </h6>
                            </div>
                            <div class="card-body">
                                <div t-if="watchlist_item.price_alert_above" class="mb-2">
                                    <strong>Alert Above:</strong>
                                    <span class="float-right" t-esc="watchlist_item.price_alert_above" t-options="{'widget': 'monetary', 'display_currency': symbol.currency}"/>
                                </div>
                                <div t-if="watchlist_item.price_alert_below">
                                    <strong>Alert Below:</strong>
                                    <span class="float-right" t-esc="watchlist_item.price_alert_below" t-options="{'widget': 'monetary', 'display_currency': symbol.currency}"/>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Upcoming Events -->
                        <div class="card" t-if="upcoming_events">
                            <div class="card-header">
                                <h6 class="mb-0">Upcoming Events</h6>
                            </div>
                            <div class="card-body">
                                <div t-foreach="upcoming_events" t-as="event" class="mb-3">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <div class="font-weight-bold" t-esc="event.title"/>
                                            <small class="text-muted d-block" t-esc="event.date" t-options="{'widget': 'datetime'}"/>
                                        </div>
                                        <span t-attf-class="badge badge-sm #{event.impact_level == 'high' and 'badge-warning' or event.impact_level == 'critical' and 'badge-danger' or 'badge-info'}" 
                                              t-esc="event.impact_level.title()"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- JavaScript -->
                <script type="text/javascript">
                    document.addEventListener('DOMContentLoaded', function() {
                        // Initialize chart for watchlist detail
                        initTradingViewChart('<t t-esc="symbol.symbol"/>', '<t t-esc="symbol.slug"/>');
                        
                        // Update last viewed
                        fetch(`/my/watchlist/<t t-esc="watchlist_item.id"/>/update-viewed`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        });
                    });
                </script>
            </t>
        </template>

        <!-- Watchlist Edit Template -->
        <template id="portal_watchlist_edit" name="Edit Watchlist Item">
            <t t-call="portal.portal_layout">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    Edit Watchlist Settings - <span t-esc="watchlist_item.symbol_id.symbol"/>
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="post">
                                    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="category">Category</label>
                                                <select class="form-control" id="category" name="category">
                                                    <option value="stocks" t-att-selected="watchlist_item.category == 'stocks' and 'selected' or None">Stocks</option>
                                                    <option value="crypto" t-att-selected="watchlist_item.category == 'crypto' and 'selected' or None">Cryptocurrency</option>
                                                    <option value="forex" t-att-selected="watchlist_item.category == 'forex' and 'selected' or None">Forex</option>
                                                    <option value="commodities" t-att-selected="watchlist_item.category == 'commodities' and 'selected' or None">Commodities</option>
                                                    <option value="favorites" t-att-selected="watchlist_item.category == 'favorites' and 'selected' or None">Favorites</option>
                                                    <option value="custom" t-att-selected="watchlist_item.category == 'custom' and 'selected' or None">Custom</option>
                                                </select>
                                            </div>

                                            <div class="form-group" id="custom-category-group" style="display: none;">
                                                <label for="custom_category">Custom Category Name</label>
                                                <input type="text" class="form-control" id="custom_category" name="custom_category"
                                                       t-att-value="watchlist_item.custom_category"/>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="target_price">Target Price</label>
                                                <input type="number" class="form-control" id="target_price" name="target_price"
                                                       step="0.01" t-att-value="watchlist_item.target_price"/>
                                            </div>

                                            <div class="form-group">
                                                <label for="stop_loss">Stop Loss</label>
                                                <input type="number" class="form-control" id="stop_loss" name="stop_loss"
                                                       step="0.01" t-att-value="watchlist_item.stop_loss"/>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="notes">Notes</label>
                                        <textarea class="form-control" id="notes" name="notes" rows="3"
                                                  placeholder="Personal notes about this symbol" t-esc="watchlist_item.notes"/>
                                    </div>

                                    <hr/>

                                    <h6>Price Alerts</h6>

                                    <div class="form-group">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="price_alert_enabled"
                                                   name="price_alert_enabled" t-att-checked="watchlist_item.price_alert_enabled and 'checked' or None"/>
                                            <label class="form-check-label" for="price_alert_enabled">
                                                Enable Price Alerts
                                            </label>
                                        </div>
                                    </div>

                                    <div id="price-alert-settings" style="display: none;">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="price_alert_above">Alert when price goes above</label>
                                                    <input type="number" class="form-control" id="price_alert_above"
                                                           name="price_alert_above" step="0.01" t-att-value="watchlist_item.price_alert_above"/>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="price_alert_below">Alert when price goes below</label>
                                                    <input type="number" class="form-control" id="price_alert_below"
                                                           name="price_alert_below" step="0.01" t-att-value="watchlist_item.price_alert_below"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group mt-4">
                                        <button type="submit" class="btn btn-primary">Save Changes</button>
                                        <a t-attf-href="/my/watchlist/#{watchlist_item.id}" class="btn btn-secondary ml-2">Cancel</a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <script type="text/javascript">
                    document.addEventListener('DOMContentLoaded', function() {
                        const categorySelect = document.getElementById('category');
                        const customCategoryGroup = document.getElementById('custom-category-group');
                        const priceAlertCheckbox = document.getElementById('price_alert_enabled');
                        const priceAlertSettings = document.getElementById('price-alert-settings');

                        // Show/hide custom category
                        function toggleCustomCategory() {
                            if (categorySelect.value === 'custom') {
                                customCategoryGroup.style.display = 'block';
                            } else {
                                customCategoryGroup.style.display = 'none';
                            }
                        }

                        // Show/hide price alert settings
                        function togglePriceAlerts() {
                            if (priceAlertCheckbox.checked) {
                                priceAlertSettings.style.display = 'block';
                            } else {
                                priceAlertSettings.style.display = 'none';
                            }
                        }

                        categorySelect.addEventListener('change', toggleCustomCategory);
                        priceAlertCheckbox.addEventListener('change', togglePriceAlerts);

                        // Initial state
                        toggleCustomCategory();
                        togglePriceAlerts();
                    });
                </script>
            </t>
        </template>

        <!-- My Alerts Template -->
        <template id="portal_my_alerts" name="My Price Alerts">
            <t t-call="portal.portal_layout">
                <div class="o_portal_wrap">
                    <div class="o_portal_my_doc_table">
                        <h3>My Price Alerts</h3>

                        <t t-if="not alerts">
                            <div class="alert alert-info text-center" role="alert">
                                <h4 class="alert-heading">No price alerts set</h4>
                                <p>You haven't set up any price alerts yet. Add symbols to your watchlist and configure alerts to get notified when prices reach your target levels.</p>
                                <a href="/my/watchlist" class="btn btn-primary">Go to Watchlist</a>
                            </div>
                        </t>

                        <t t-if="alerts">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="thead-light">
                                        <tr>
                                            <th>Symbol</th>
                                            <th>Current Price</th>
                                            <th>Alert Above</th>
                                            <th>Alert Below</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr t-foreach="alerts" t-as="alert">
                                            <td>
                                                <a t-attf-href="/market/#{alert.symbol_id.slug}" class="font-weight-bold text-decoration-none">
                                                    <t t-esc="alert.symbol_id.symbol"/>
                                                </a>
                                                <br/>
                                                <small class="text-muted" t-esc="alert.symbol_id.name"/>
                                            </td>
                                            <td>
                                                <span t-if="alert.current_price" t-esc="alert.current_price" t-options="{'widget': 'monetary', 'display_currency': alert.symbol_id.currency}"/>
                                                <span t-else="">--</span>
                                            </td>
                                            <td>
                                                <span t-if="alert.price_alert_above" t-esc="alert.price_alert_above" t-options="{'widget': 'monetary', 'display_currency': alert.symbol_id.currency}"/>
                                                <span t-else="">--</span>
                                            </td>
                                            <td>
                                                <span t-if="alert.price_alert_below" t-esc="alert.price_alert_below" t-options="{'widget': 'monetary', 'display_currency': alert.symbol_id.currency}"/>
                                                <span t-else="">--</span>
                                            </td>
                                            <td>
                                                <span class="badge badge-success">Active</span>
                                            </td>
                                            <td>
                                                <a t-attf-href="/my/watchlist/#{alert.id}/edit" class="btn btn-outline-primary btn-sm">
                                                    <i class="fa fa-edit"/> Edit
                                                </a>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </t>
                    </div>
                </div>
            </t>
        </template>

        <!-- My Portfolio Template -->
        <template id="portal_my_portfolio" name="My Portfolio">
            <t t-call="portal.portal_layout">
                <div class="o_portal_wrap">
                    <h3>Portfolio Overview</h3>

                    <!-- Portfolio Summary -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h3 class="text-primary" t-esc="portfolio_stats['total_symbols']"/>
                                    <p class="mb-0">Total Symbols</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h3 t-attf-class="#{portfolio_stats['total_change'] >= 0 and 'text-success' or 'text-danger'}">
                                        <span t-esc="portfolio_stats['total_change']" t-options="{'widget': 'monetary'}"/>
                                    </h3>
                                    <p class="mb-0">Total Change</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h3 t-attf-class="#{portfolio_stats['total_change_percent'] >= 0 and 'text-success' or 'text-danger'}">
                                        <span t-esc="portfolio_stats['total_change_percent']" t-options="{'precision': 2}"/>%
                                    </h3>
                                    <p class="mb-0">Total Change %</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h3 class="text-info">
                                        <span class="text-success" t-esc="portfolio_stats['positive_count']"/>/<span class="text-danger" t-esc="portfolio_stats['negative_count']"/>
                                    </h3>
                                    <p class="mb-0">Winners/Losers</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Performance by Category -->
                    <div class="row mb-4">
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Performance by Category</h6>
                                </div>
                                <div class="card-body">
                                    <div t-foreach="category_performance" t-as="cat_name" class="mb-3">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="font-weight-bold" t-esc="cat_name.title()"/>
                                            <span t-attf-class="#{category_performance[cat_name]['avg_change'] >= 0 and 'text-success' or 'text-danger'}">
                                                <t t-esc="category_performance[cat_name]['avg_change']" t-options="{'precision': 2}"/>%
                                            </span>
                                        </div>
                                        <small class="text-muted">
                                            <t t-esc="category_performance[cat_name]['count']"/> symbols
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Top Performers</h6>
                                </div>
                                <div class="card-body">
                                    <div t-foreach="top_performers" t-as="performer" class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <strong t-esc="performer.symbol_id.symbol"/>
                                            <small class="text-muted d-block" t-esc="performer.symbol_id.name"/>
                                        </div>
                                        <span class="text-success">
                                            +<t t-esc="performer.performance_since_added" t-options="{'precision': 2}"/>%
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

    </data>
</odoo>
