<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Symbol Tree View -->
        <record id="view_tradingview_symbol_tree" model="ir.ui.view">
            <field name="name">tradingview.symbol.tree</field>
            <field name="model">tradingview.symbol</field>
            <field name="arch" type="xml">
                <tree string="Financial Symbols" default_order="symbol">
                    <field name="symbol" string="Symbol"/>
                    <field name="name" string="Name"/>
                    <field name="type" string="Type"/>
                    <field name="exchange" string="Exchange"/>
                    <field name="region" string="Region"/>
                    <field name="current_price" string="Price" widget="monetary" options="{'currency_field': 'currency'}"/>
                    <field name="daily_change" string="Change" decoration-success="daily_change > 0" decoration-danger="daily_change &lt; 0"/>
                    <field name="daily_change_percent" string="Change %" decoration-success="daily_change_percent > 0" decoration-danger="daily_change_percent &lt; 0"/>
                    <field name="volume" string="Volume" widget="integer"/>
                    <field name="market_cap" string="Market Cap" widget="monetary" options="{'currency_field': 'currency'}"/>
                    <field name="last_updated" string="Last Updated"/>
                    <field name="active" string="Active"/>
                    <field name="currency" invisible="1"/>
                </tree>
            </field>
        </record>
        
        <!-- Symbol Form View -->
        <record id="view_tradingview_symbol_form" model="ir.ui.view">
            <field name="name">tradingview.symbol.form</field>
            <field name="model">tradingview.symbol</field>
            <field name="arch" type="xml">
                <form string="Financial Symbol">
                    <header>
                        <button name="action_sync_data" type="object" string="Sync Data" class="btn-primary"/>
                        <field name="active" widget="boolean_toggle"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <!-- Stat buttons will be added when corresponding view files are enabled -->
                        </div>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="symbol" placeholder="Symbol (e.g., AAPL, BTCUSD)"/>
                            </h1>
                            <h2>
                                <field name="name" placeholder="Full Name"/>
                            </h2>
                        </div>
                        
                        <group>
                            <group name="basic_info" string="Basic Information">
                                <field name="type"/>
                                <field name="slug"/>
                                <field name="exchange"/>
                                <field name="region"/>
                                <field name="currency"/>
                                <field name="isin"/>
                            </group>
                            <group name="classification" string="Classification">
                                <field name="sector"/>
                                <field name="industry"/>
                                <field name="description"/>
                                <field name="website" widget="url"/>
                                <field name="logo_url" widget="url"/>
                            </group>
                        </group>
                        
                        <group>
                            <group name="current_data" string="Current Market Data">
                                <field name="current_price" widget="monetary" options="{'currency_field': 'currency'}"/>
                                <field name="daily_change" decoration-success="daily_change > 0" decoration-danger="daily_change &lt; 0"/>
                                <field name="daily_change_percent" decoration-success="daily_change_percent > 0" decoration-danger="daily_change_percent &lt; 0"/>
                                <field name="volume" widget="integer"/>
                                <field name="market_cap" widget="monetary" options="{'currency_field': 'currency'}"/>
                                <field name="last_updated"/>
                            </group>
                            <group name="price_ranges" string="Price Ranges">
                                <field name="day_high" widget="monetary" options="{'currency_field': 'currency'}"/>
                                <field name="day_low" widget="monetary" options="{'currency_field': 'currency'}"/>
                                <field name="week_52_high" widget="monetary" options="{'currency_field': 'currency'}"/>
                                <field name="week_52_low" widget="monetary" options="{'currency_field': 'currency'}"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="OHLC Data" name="ohlc_data">
                                <field name="ohlc_ids" context="{'default_symbol_id': active_id}">
                                    <tree limit="20" default_order="timestamp desc">
                                        <field name="timestamp"/>
                                        <field name="timeframe"/>
                                        <field name="open"/>
                                        <field name="high"/>
                                        <field name="low"/>
                                        <field name="close"/>
                                        <field name="volume"/>
                                        <field name="price_change" decoration-success="price_change > 0" decoration-danger="price_change &lt; 0"/>
                                        <field name="price_change_percent" decoration-success="price_change_percent > 0" decoration-danger="price_change_percent &lt; 0"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="Technical Indicators" name="technical">
                                <field name="technical_ids" context="{'default_symbol_id': active_id}">
                                    <tree limit="20" default_order="timestamp desc">
                                        <field name="timestamp"/>
                                        <field name="indicator"/>
                                        <field name="value"/>
                                        <field name="signal"/>
                                        <field name="signal_strength"/>
                                        <field name="timeframe"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="News" name="news">
                                <field name="news_ids" context="{'default_symbol_id': active_id}">
                                    <tree limit="10" default_order="published_at desc">
                                        <field name="published_at"/>
                                        <field name="title"/>
                                        <field name="source"/>
                                        <field name="category"/>
                                        <field name="sentiment"/>
                                        <field name="impact_level"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="Events" name="events">
                                <field name="event_ids" context="{'default_symbol_id': active_id}">
                                    <tree limit="10" default_order="date asc">
                                        <field name="date"/>
                                        <field name="title"/>
                                        <field name="event_type"/>
                                        <field name="impact_level"/>
                                        <field name="status"/>
                                        <field name="is_upcoming"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- Symbol Search View -->
        <record id="view_tradingview_symbol_search" model="ir.ui.view">
            <field name="name">tradingview.symbol.search</field>
            <field name="model">tradingview.symbol</field>
            <field name="arch" type="xml">
                <search string="Search Symbols">
                    <field name="symbol" string="Symbol" filter_domain="[('symbol', 'ilike', self)]"/>
                    <field name="name" string="Name" filter_domain="[('name', 'ilike', self)]"/>
                    <field name="exchange"/>
                    <field name="sector"/>
                    <field name="industry"/>
                    
                    <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                    <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                    
                    <separator/>
                    <filter string="Stocks" name="stocks" domain="[('type', '=', 'stock')]"/>
                    <filter string="Cryptocurrency" name="crypto" domain="[('type', '=', 'crypto')]"/>
                    <filter string="Forex" name="forex" domain="[('type', '=', 'forex')]"/>
                    <filter string="Commodities" name="commodities" domain="[('type', '=', 'commodity')]"/>
                    <filter string="Indices" name="indices" domain="[('type', '=', 'index')]"/>
                    
                    <separator/>
                    <filter string="Recently Updated" name="recent" 
                            domain="[('last_updated', '>=', (context_today() - datetime.timedelta(days=1)).strftime('%Y-%m-%d'))]"/>
                    <filter string="High Volume" name="high_volume" domain="[('volume', '>', 1000000)]"/>
                    
                    <group expand="0" string="Group By">
                        <filter string="Type" name="group_type" context="{'group_by': 'type'}"/>
                        <filter string="Exchange" name="group_exchange" context="{'group_by': 'exchange'}"/>
                        <filter string="Region" name="group_region" context="{'group_by': 'region'}"/>
                        <filter string="Sector" name="group_sector" context="{'group_by': 'sector'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- Symbol Kanban View -->
        <record id="view_tradingview_symbol_kanban" model="ir.ui.view">
            <field name="name">tradingview.symbol.kanban</field>
            <field name="model">tradingview.symbol</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <field name="symbol"/>
                    <field name="name"/>
                    <field name="type"/>
                    <field name="current_price"/>
                    <field name="daily_change"/>
                    <field name="daily_change_percent"/>
                    <field name="currency"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="symbol"/>
                                        </strong>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            <field name="name"/>
                                        </small>
                                    </div>
                                    <div class="o_kanban_record_body">
                                        <div class="row">
                                            <div class="col-6">
                                                <span class="badge badge-pill badge-info">
                                                    <field name="type"/>
                                                </span>
                                            </div>
                                            <div class="col-6 text-right">
                                                <strong>
                                                    <field name="current_price" widget="monetary" options="{'currency_field': 'currency'}"/>
                                                </strong>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-12">
                                                <span t-attf-class="badge badge-pill #{record.daily_change.raw_value >= 0 ? 'badge-success' : 'badge-danger'}">
                                                    <field name="daily_change"/> (<field name="daily_change_percent"/>%)
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>
        
        <!-- Actions -->
        <record id="action_tradingview_symbol" model="ir.actions.act_window">
            <field name="name">All Symbols</field>
            <field name="res_model">tradingview.symbol</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="context">{'search_default_active': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first financial symbol!
                </p>
                <p>
                    Add stocks, cryptocurrencies, forex pairs, commodities, and indices to start tracking market data.
                </p>
            </field>
        </record>
        
        <record id="action_tradingview_symbol_stocks" model="ir.actions.act_window">
            <field name="name">Stocks</field>
            <field name="res_model">tradingview.symbol</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="domain">[('type', '=', 'stock')]</field>
            <field name="context">{'search_default_active': 1, 'default_type': 'stock'}</field>
        </record>
        
        <record id="action_tradingview_symbol_crypto" model="ir.actions.act_window">
            <field name="name">Cryptocurrency</field>
            <field name="res_model">tradingview.symbol</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="domain">[('type', '=', 'crypto')]</field>
            <field name="context">{'search_default_active': 1, 'default_type': 'crypto'}</field>
        </record>
        
        <record id="action_tradingview_symbol_forex" model="ir.actions.act_window">
            <field name="name">Forex</field>
            <field name="res_model">tradingview.symbol</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="domain">[('type', '=', 'forex')]</field>
            <field name="context">{'search_default_active': 1, 'default_type': 'forex'}</field>
        </record>
        
        <record id="action_tradingview_symbol_commodities" model="ir.actions.act_window">
            <field name="name">Commodities</field>
            <field name="res_model">tradingview.symbol</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="domain">[('type', '=', 'commodity')]</field>
            <field name="context">{'search_default_active': 1, 'default_type': 'commodity'}</field>
        </record>
        
    </data>
</odoo>
