<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Sync Log Tree View -->
        <record id="view_tradingview_sync_log_tree" model="ir.ui.view">
            <field name="name">tradingview.sync_log.tree</field>
            <field name="model">tradingview.sync_log</field>
            <field name="arch" type="xml">
                <tree string="Sync Logs" default_order="start_time desc">
                    <field name="start_time" string="Start Time"/>
                    <field name="api_name" string="API"/>
                    <field name="sync_type" string="Type"/>
                    <field name="status" string="Status" 
                           decoration-success="status == 'success'" 
                           decoration-warning="status == 'partial_success'"
                           decoration-danger="status == 'failure'"
                           decoration-info="status == 'running'"/>
                    <field name="duration_seconds" string="Duration (s)" widget="float_time"/>
                    <field name="records_processed" string="Processed"/>
                    <field name="records_created" string="Created"/>
                    <field name="records_updated" string="Updated"/>
                    <field name="records_failed" string="Failed"/>
                    <field name="success_rate" string="Success Rate %" widget="progressbar"/>
                    <field name="api_calls_made" string="API Calls"/>
                    <field name="rate_limit_hit" string="Rate Limited" widget="boolean_toggle"/>
                    <field name="triggered_by" string="Triggered By"/>
                    <field name="user_id" string="User"/>
                </tree>
            </field>
        </record>
        
        <!-- Sync Log Form View -->
        <record id="view_tradingview_sync_log_form" model="ir.ui.view">
            <field name="name">tradingview.sync_log.form</field>
            <field name="model">tradingview.sync_log</field>
            <field name="arch" type="xml">
                <form string="Sync Log">
                    <header>
                        <button name="retry_sync" type="object" string="Retry Sync" class="btn-primary" 
                                attrs="{'invisible': [('status', '!=', 'failure')]}"/>
                        <field name="status" widget="statusbar" statusbar_visible="running,success,failure"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="display_name"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group name="basic_info" string="Basic Information">
                                <field name="api_name"/>
                                <field name="sync_type"/>
                                <field name="triggered_by"/>
                                <field name="user_id"/>
                            </group>
                            <group name="timing" string="Timing">
                                <field name="start_time"/>
                                <field name="end_time"/>
                                <field name="duration_seconds" widget="float_time"/>
                                <field name="is_recent"/>
                            </group>
                        </group>
                        
                        <group>
                            <group name="metrics" string="Processing Metrics">
                                <field name="records_processed"/>
                                <field name="records_created"/>
                                <field name="records_updated"/>
                                <field name="records_failed"/>
                                <field name="success_rate" widget="progressbar"/>
                            </group>
                            <group name="api_metrics" string="API Metrics">
                                <field name="api_calls_made"/>
                                <field name="rate_limit_hit"/>
                                <field name="rate_limit_reset_time" attrs="{'invisible': [('rate_limit_hit', '=', False)]}"/>
                                <field name="requests_remaining" attrs="{'invisible': [('rate_limit_hit', '=', False)]}"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Parameters" name="parameters">
                                <field name="parameters" widget="text"/>
                            </page>
                            <page string="Symbols Synced" name="symbols">
                                <field name="symbols_synced" widget="text"/>
                            </page>
                            <page string="Error Details" name="errors" attrs="{'invisible': [('status', 'not in', ['failure', 'partial_success'])]}">
                                <group>
                                    <field name="error_message" widget="text"/>
                                    <field name="error_details" widget="text"/>
                                </group>
                            </page>
                            <page string="Warnings" name="warnings" attrs="{'invisible': [('warnings', '=', False)]}">
                                <field name="warnings" widget="text"/>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>
        
        <!-- Sync Log Search View -->
        <record id="view_tradingview_sync_log_search" model="ir.ui.view">
            <field name="name">tradingview.sync_log.search</field>
            <field name="model">tradingview.sync_log</field>
            <field name="arch" type="xml">
                <search string="Search Sync Logs">
                    <field name="api_name"/>
                    <field name="sync_type"/>
                    <field name="user_id"/>
                    <field name="error_message"/>
                    
                    <filter string="Running" name="running" domain="[('status', '=', 'running')]"/>
                    <filter string="Success" name="success" domain="[('status', '=', 'success')]"/>
                    <filter string="Partial Success" name="partial_success" domain="[('status', '=', 'partial_success')]"/>
                    <filter string="Failed" name="failed" domain="[('status', '=', 'failure')]"/>
                    
                    <separator/>
                    <filter string="Recent" name="recent" domain="[('is_recent', '=', True)]"/>
                    <filter string="Rate Limited" name="rate_limited" domain="[('rate_limit_hit', '=', True)]"/>
                    <filter string="With Errors" name="with_errors" domain="[('error_message', '!=', False)]"/>
                    
                    <separator/>
                    <filter string="Today" name="today" 
                            domain="[('start_time', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))),
                                     ('start_time', '&lt;', datetime.datetime.combine(context_today() + datetime.timedelta(days=1), datetime.time(0,0,0)))]"/>
                    <filter string="This Week" name="this_week" 
                            domain="[('start_time', '>=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                    <filter string="This Month" name="this_month" 
                            domain="[('start_time', '>=', context_today().strftime('%Y-%m-01'))]"/>
                    
                    <separator/>
                    <filter string="Manual" name="manual" domain="[('triggered_by', '=', 'manual')]"/>
                    <filter string="Cron" name="cron" domain="[('triggered_by', '=', 'cron')]"/>
                    <filter string="API" name="api" domain="[('triggered_by', '=', 'api')]"/>
                    
                    <separator/>
                    <filter string="TwelveData" name="twelvedata" domain="[('api_name', '=', 'twelvedata')]"/>
                    <filter string="Binance" name="binance" domain="[('api_name', '=', 'binance')]"/>
                    <filter string="NewsAPI" name="newsapi" domain="[('api_name', '=', 'newsapi')]"/>
                    
                    <group expand="0" string="Group By">
                        <filter string="API Name" name="group_api" context="{'group_by': 'api_name'}"/>
                        <filter string="Sync Type" name="group_type" context="{'group_by': 'sync_type'}"/>
                        <filter string="Status" name="group_status" context="{'group_by': 'status'}"/>
                        <filter string="Triggered By" name="group_triggered" context="{'group_by': 'triggered_by'}"/>
                        <filter string="User" name="group_user" context="{'group_by': 'user_id'}"/>
                        <filter string="Date" name="group_date" context="{'group_by': 'start_time:day'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- Sync Log Graph View -->
        <record id="view_tradingview_sync_log_graph" model="ir.ui.view">
            <field name="name">tradingview.sync_log.graph</field>
            <field name="model">tradingview.sync_log</field>
            <field name="arch" type="xml">
                <graph string="Sync Statistics" type="bar">
                    <field name="start_time" type="row" interval="day"/>
                    <field name="records_processed" type="measure"/>
                    <field name="api_calls_made" type="measure"/>
                    <field name="duration_seconds" type="measure"/>
                </graph>
            </field>
        </record>
        
        <!-- Sync Log Pivot View -->
        <record id="view_tradingview_sync_log_pivot" model="ir.ui.view">
            <field name="name">tradingview.sync_log.pivot</field>
            <field name="model">tradingview.sync_log</field>
            <field name="arch" type="xml">
                <pivot string="Sync Analysis">
                    <field name="api_name" type="row"/>
                    <field name="sync_type" type="col"/>
                    <field name="records_processed" type="measure"/>
                    <field name="success_rate" type="measure"/>
                    <field name="duration_seconds" type="measure"/>
                </pivot>
            </field>
        </record>
        
        <!-- Sync Log Kanban View -->
        <record id="view_tradingview_sync_log_kanban" model="ir.ui.view">
            <field name="name">tradingview.sync_log.kanban</field>
            <field name="model">tradingview.sync_log</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile" default_group_by="status">
                    <field name="api_name"/>
                    <field name="sync_type"/>
                    <field name="status"/>
                    <field name="start_time"/>
                    <field name="duration_seconds"/>
                    <field name="records_processed"/>
                    <field name="success_rate"/>
                    <field name="rate_limit_hit"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="api_name"/> - <field name="sync_type"/>
                                        </strong>
                                        <div class="o_kanban_record_subtitle">
                                            <field name="start_time"/>
                                        </div>
                                    </div>
                                    <div class="o_kanban_record_body">
                                        <div class="row">
                                            <div class="col-6">
                                                <strong>Processed: <field name="records_processed"/></strong>
                                            </div>
                                            <div class="col-6 text-right">
                                                <strong>Duration: <field name="duration_seconds"/>s</strong>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-12">
                                                <div class="progress" style="height: 10px;">
                                                    <div t-attf-class="progress-bar #{record.status.raw_value === 'success' ? 'bg-success' : record.status.raw_value === 'failure' ? 'bg-danger' : record.status.raw_value === 'partial_success' ? 'bg-warning' : 'bg-info'}" 
                                                         role="progressbar" 
                                                         t-attf-style="width: #{record.success_rate.raw_value}%"
                                                         t-attf-aria-valuenow="#{record.success_rate.raw_value}" 
                                                         aria-valuemin="0" aria-valuemax="100">
                                                    </div>
                                                </div>
                                                <small class="text-muted">
                                                    Success Rate: <field name="success_rate"/>%
                                                </small>
                                            </div>
                                        </div>
                                        <div class="row mt-1" t-if="record.rate_limit_hit.raw_value">
                                            <div class="col-12">
                                                <span class="badge badge-warning">
                                                    <i class="fa fa-exclamation-triangle"/> Rate Limited
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>
        
        <!-- Action -->
        <record id="action_tradingview_sync_log" model="ir.actions.act_window">
            <field name="name">Sync Logs</field>
            <field name="res_model">tradingview.sync_log</field>
            <field name="view_mode">tree,kanban,form,graph,pivot</field>
            <field name="context">{'search_default_recent': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No sync logs found!
                </p>
                <p>
                    Data synchronization logs will appear here once you start syncing data from external APIs.
                </p>
            </field>
        </record>
        
        <!-- Failed Syncs Action -->
        <record id="action_tradingview_failed_syncs" model="ir.actions.act_window">
            <field name="name">Failed Syncs</field>
            <field name="res_model">tradingview.sync_log</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('status', '=', 'failure')]</field>
            <field name="context">{'search_default_recent': 1}</field>
        </record>
        
    </data>
</odoo>
