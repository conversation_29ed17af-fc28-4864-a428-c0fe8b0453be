<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Sync Wizard Form View -->
        <record id="view_tradingview_sync_wizard_form" model="ir.ui.view">
            <field name="name">tradingview.sync.wizard.form</field>
            <field name="model">tradingview.sync.wizard</field>
            <field name="arch" type="xml">
                <form string="Manual Data Synchronization">
                    <sheet>
                        <div class="oe_title">
                            <h1>Manual Data Synchronization</h1>
                            <p>Configure and start manual synchronization of financial data from external APIs.</p>
                        </div>
                        
                        <group string="Synchronization Options">
                            <group name="sync_options" string="Data Types">
                                <field name="sync_symbols"/>
                                <field name="sync_ohlc"/>
                                <field name="sync_news"/>
                                <field name="sync_events"/>
                                <field name="sync_technical"/>
                            </group>
                            <group name="api_sources" string="API Sources">
                                <field name="use_twelvedata"/>
                                <field name="use_binance"/>
                                <field name="use_newsapi"/>
                            </group>
                        </group>
                        
                        <group string="Symbol Selection">
                            <group name="symbol_selection" string="Symbol Types">
                                <field name="symbol_types"/>
                                <field name="symbol_ids" widget="many2many_tags"
                                       invisible="symbol_types != 'custom'" required="symbol_types == 'custom'"/>
                            </group>
                            <group name="sync_options_advanced" string="Advanced Options">
                                <field name="days_back"/>
                                <field name="limit_records"/>
                                <field name="force_update"/>
                                <field name="test_mode"/>
                            </group>
                        </group>
                        
                        <!-- Results Section (shown after sync) -->
                        <group string="Synchronization Results" invisible="not sync_log_ids">
                            <field name="sync_log_ids" nolabel="1">
                                <tree string="Sync Logs">
                                    <field name="api_name"/>
                                    <field name="sync_type"/>
                                    <field name="status" decoration-success="status == 'success'" decoration-danger="status == 'failure'"/>
                                    <field name="records_processed"/>
                                    <field name="records_created"/>
                                    <field name="records_failed"/>
                                    <field name="duration_seconds"/>
                                    <field name="start_time"/>
                                </tree>
                            </field>
                        </group>
                    </sheet>
                    <footer>
                        <button name="action_start_sync" type="object" string="Start Synchronization"
                                class="btn-primary" invisible="sync_log_ids"/>
                        <button name="action_view_sync_logs" type="object" string="View Detailed Logs"
                                class="btn-secondary" invisible="not sync_log_ids"/>
                        <button name="action_close" type="object" string="Close" class="btn-secondary"/>
                    </footer>
                </form>
            </field>
        </record>
        
        <!-- Sync Wizard Action -->
        <record id="action_tradingview_sync_wizard" model="ir.actions.act_window">
            <field name="name">Manual Data Synchronization</field>
            <field name="res_model">tradingview.sync.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Manual Data Synchronization
                </p>
                <p>
                    Use this wizard to manually synchronize financial data from external APIs including symbols, OHLC data, news, and events.
                </p>
            </field>
        </record>
        
    </data>
</odoo>
