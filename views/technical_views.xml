<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Technical Indicators Tree View -->
        <record id="view_tradingview_technical_tree" model="ir.ui.view">
            <field name="name">tradingview.technical.tree</field>
            <field name="model">tradingview.technical</field>
            <field name="arch" type="xml">
                <tree string="Technical Indicators" default_order="timestamp desc">
                    <field name="symbol_id" string="Symbol"/>
                    <field name="timestamp" string="Timestamp"/>
                    <field name="indicator" string="Indicator"/>
                    <field name="value" string="Value" widget="float" digits="[16,6]"/>
                    <field name="signal" string="Signal" decoration-success="signal in ('buy', 'strong_buy')" decoration-danger="signal in ('sell', 'strong_sell')"/>
                    <field name="signal_strength" string="Strength" widget="progressbar"/>
                    <field name="timeframe" string="Timeframe"/>
                    <field name="indicator_category" string="Category"/>
                    <field name="period" string="Period"/>
                    <field name="data_source" string="Source"/>
                </tree>
            </field>
        </record>
        
        <!-- Technical Indicators Form View -->
        <record id="view_tradingview_technical_form" model="ir.ui.view">
            <field name="name">tradingview.technical.form</field>
            <field name="model">tradingview.technical</field>
            <field name="arch" type="xml">
                <form string="Technical Indicator">
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="display_name"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group name="basic_info" string="Basic Information">
                                <field name="symbol_id"/>
                                <field name="indicator"/>
                                <field name="custom_name" invisible="indicator != 'custom'" required="indicator == 'custom'"/>
                                <field name="timestamp"/>
                                <field name="timeframe"/>
                                <field name="indicator_category"/>
                            </group>
                            <group name="calculation" string="Calculation">
                                <field name="value" widget="float" digits="[16,6]"/>
                                <field name="period"/>
                                <field name="parameters"/>
                                <field name="calculation_method"/>
                                <field name="data_source"/>
                            </group>
                        </group>
                        
                        <group>
                            <group name="signal_info" string="Signal Information">
                                <field name="signal"/>
                                <field name="signal_strength" widget="progressbar"/>
                            </group>
                            <group name="metadata" string="Metadata">
                                <field name="created_date"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- Technical Indicators Search View -->
        <record id="view_tradingview_technical_search" model="ir.ui.view">
            <field name="name">tradingview.technical.search</field>
            <field name="model">tradingview.technical</field>
            <field name="arch" type="xml">
                <search string="Search Technical Indicators">
                    <field name="symbol_id" string="Symbol"/>
                    <field name="indicator"/>
                    <field name="timestamp"/>
                    <field name="timeframe"/>
                    <field name="data_source"/>
                    
                    <filter string="Today" name="today" 
                            domain="[('timestamp', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))),
                                     ('timestamp', '&lt;', datetime.datetime.combine(context_today() + datetime.timedelta(days=1), datetime.time(0,0,0)))]"/>
                    <filter string="This Week" name="this_week" 
                            domain="[('timestamp', '>=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                    
                    <separator/>
                    <filter string="Buy Signals" name="buy_signals" domain="[('signal', 'in', ['buy', 'strong_buy'])]"/>
                    <filter string="Sell Signals" name="sell_signals" domain="[('signal', 'in', ['sell', 'strong_sell'])]"/>
                    <filter string="Neutral" name="neutral" domain="[('signal', '=', 'neutral')]"/>
                    
                    <separator/>
                    <filter string="Trend Indicators" name="trend" domain="[('indicator_category', '=', 'trend')]"/>
                    <filter string="Momentum Indicators" name="momentum" domain="[('indicator_category', '=', 'momentum')]"/>
                    <filter string="Volume Indicators" name="volume" domain="[('indicator_category', '=', 'volume')]"/>
                    <filter string="Volatility Indicators" name="volatility" domain="[('indicator_category', '=', 'volatility')]"/>
                    
                    <separator/>
                    <filter string="RSI" name="rsi" domain="[('indicator', 'in', ['rsi', 'rsi_14'])]"/>
                    <filter string="MACD" name="macd" domain="[('indicator', 'in', ['macd_line', 'macd_signal', 'macd_histogram'])]"/>
                    <filter string="Moving Averages" name="ma" domain="[('indicator', 'in', ['sma_20', 'sma_50', 'sma_200', 'ema_12', 'ema_26', 'ema_50', 'ema_200'])]"/>
                    <filter string="Bollinger Bands" name="bb" domain="[('indicator', 'in', ['bb_upper', 'bb_middle', 'bb_lower', 'bb_width'])]"/>
                    
                    <group expand="0" string="Group By">
                        <filter string="Symbol" name="group_symbol" context="{'group_by': 'symbol_id'}"/>
                        <filter string="Indicator" name="group_indicator" context="{'group_by': 'indicator'}"/>
                        <filter string="Category" name="group_category" context="{'group_by': 'indicator_category'}"/>
                        <filter string="Timeframe" name="group_timeframe" context="{'group_by': 'timeframe'}"/>
                        <filter string="Signal" name="group_signal" context="{'group_by': 'signal'}"/>
                        <filter string="Date" name="group_date" context="{'group_by': 'timestamp:day'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- Technical Indicators Kanban View -->
        <record id="view_tradingview_technical_kanban" model="ir.ui.view">
            <field name="name">tradingview.technical.kanban</field>
            <field name="model">tradingview.technical</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile" default_group_by="indicator_category">
                    <field name="symbol_id"/>
                    <field name="indicator"/>
                    <field name="value"/>
                    <field name="signal"/>
                    <field name="signal_strength"/>
                    <field name="indicator_category"/>
                    <field name="timestamp"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="symbol_id"/>
                                        </strong>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            <field name="indicator"/>
                                        </small>
                                    </div>
                                    <div class="o_kanban_record_body">
                                        <div class="row">
                                            <div class="col-6">
                                                <strong>Value: <field name="value"/></strong>
                                            </div>
                                            <div class="col-6 text-right">
                                                <span t-attf-class="badge badge-pill #{record.signal.raw_value === 'strong_buy' or record.signal.raw_value === 'buy' ? 'badge-success' : record.signal.raw_value === 'strong_sell' or record.signal.raw_value === 'sell' ? 'badge-danger' : 'badge-secondary'}">
                                                    <field name="signal"/>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-12">
                                                <div class="progress" style="height: 10px;">
                                                    <div class="progress-bar" role="progressbar" 
                                                         t-attf-style="width: #{record.signal_strength.raw_value}%"
                                                         t-attf-aria-valuenow="#{record.signal_strength.raw_value}" 
                                                         aria-valuemin="0" aria-valuemax="100">
                                                    </div>
                                                </div>
                                                <small class="text-muted">
                                                    Strength: <field name="signal_strength"/>%
                                                </small>
                                            </div>
                                        </div>
                                        <div class="row mt-1">
                                            <div class="col-12">
                                                <small class="text-muted">
                                                    <field name="timestamp"/>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>
        
        <!-- Technical Indicators Graph View -->
        <record id="view_tradingview_technical_graph" model="ir.ui.view">
            <field name="name">tradingview.technical.graph</field>
            <field name="model">tradingview.technical</field>
            <field name="arch" type="xml">
                <graph string="Technical Indicators Chart" type="line">
                    <field name="timestamp" type="row" interval="day"/>
                    <field name="value" type="measure"/>
                    <field name="indicator" type="col"/>
                </graph>
            </field>
        </record>
        
        <!-- Technical Indicators Pivot View -->
        <record id="view_tradingview_technical_pivot" model="ir.ui.view">
            <field name="name">tradingview.technical.pivot</field>
            <field name="model">tradingview.technical</field>
            <field name="arch" type="xml">
                <pivot string="Technical Analysis">
                    <field name="symbol_id" type="row"/>
                    <field name="indicator" type="col"/>
                    <field name="value" type="measure"/>
                    <field name="signal_strength" type="measure"/>
                </pivot>
            </field>
        </record>
        
        <!-- Action -->
        <record id="action_tradingview_technical" model="ir.actions.act_window">
            <field name="name">Technical Indicators</field>
            <field name="res_model">tradingview.technical</field>
            <field name="view_mode">tree,kanban,form,graph,pivot</field>
            <field name="context">{'search_default_today': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No technical indicators found!
                </p>
                <p>
                    Technical indicators like RSI, MACD, Moving Averages, and Bollinger Bands will appear here once you start calculating them from OHLC data.
                </p>
            </field>
        </record>
        
    </data>
</odoo>
