<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Watchlist Tree View -->
        <record id="view_tradingview_watchlist_tree" model="ir.ui.view">
            <field name="name">tradingview.watchlist.tree</field>
            <field name="model">tradingview.watchlist</field>
            <field name="arch" type="xml">
                <tree string="Watchlists" default_order="added_date desc">
                    <field name="user_id" string="User"/>
                    <field name="symbol_id" string="Symbol"/>
                    <field name="category" string="Category"/>
                    <field name="current_price" string="Current Price" widget="monetary"/>
                    <field name="daily_change" string="Daily Change" decoration-success="daily_change > 0" decoration-danger="daily_change &lt; 0"/>
                    <field name="daily_change_percent" string="Daily Change %" decoration-success="daily_change_percent > 0" decoration-danger="daily_change_percent &lt; 0"/>
                    <field name="performance_since_added" string="Performance %" decoration-success="performance_since_added > 0" decoration-danger="performance_since_added &lt; 0"/>
                    <field name="target_price" string="Target Price" widget="monetary"/>
                    <field name="stop_loss" string="Stop Loss" widget="monetary"/>
                    <field name="price_alert_enabled" string="Alert" widget="boolean_toggle"/>
                    <field name="added_date" string="Added"/>
                    <field name="view_count" string="Views"/>
                    <field name="is_active" string="Active" widget="boolean_toggle"/>
                </tree>
            </field>
        </record>
        
        <!-- Watchlist Form View -->
        <record id="view_tradingview_watchlist_form" model="ir.ui.view">
            <field name="name">tradingview.watchlist.form</field>
            <field name="model">tradingview.watchlist</field>
            <field name="arch" type="xml">
                <form string="Watchlist Item">
                    <header>
                        <button name="toggle_price_alert" type="object" string="Toggle Alert" class="btn-secondary"/>
                        <button name="remove_from_watchlist" type="object" string="Remove" class="btn-danger" confirm="Are you sure you want to remove this symbol from your watchlist?"/>
                        <field name="is_active" widget="boolean_toggle"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="display_name"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group name="basic_info" string="Basic Information">
                                <field name="user_id"/>
                                <field name="symbol_id"/>
                                <field name="category"/>
                                <field name="custom_category" attrs="{'invisible': [('category', '!=', 'custom')], 'required': [('category', '=', 'custom')]}"/>
                                <field name="added_date"/>
                            </group>
                            <group name="current_data" string="Current Market Data">
                                <field name="current_price" widget="monetary"/>
                                <field name="daily_change" decoration-success="daily_change > 0" decoration-danger="daily_change &lt; 0"/>
                                <field name="daily_change_percent" decoration-success="daily_change_percent > 0" decoration-danger="daily_change_percent &lt; 0"/>
                                <field name="price_when_added" widget="monetary"/>
                                <field name="performance_since_added" decoration-success="performance_since_added > 0" decoration-danger="performance_since_added &lt; 0"/>
                            </group>
                        </group>
                        
                        <group>
                            <group name="user_targets" string="User Targets">
                                <field name="target_price" widget="monetary"/>
                                <field name="stop_loss" widget="monetary"/>
                                <field name="notes" placeholder="Personal notes about this symbol"/>
                            </group>
                            <group name="tracking" string="Tracking">
                                <field name="last_viewed"/>
                                <field name="view_count"/>
                            </group>
                        </group>
                        
                        <group string="Price Alerts">
                            <group name="price_alerts" string="Price Alert Settings">
                                <field name="price_alert_enabled"/>
                                <field name="price_alert_above" widget="monetary" attrs="{'invisible': [('price_alert_enabled', '=', False)]}"/>
                                <field name="price_alert_below" widget="monetary" attrs="{'invisible': [('price_alert_enabled', '=', False)]}"/>
                            </group>
                            <group name="volume_alerts" string="Volume Alert Settings">
                                <field name="volume_alert_enabled"/>
                                <field name="volume_alert_threshold" attrs="{'invisible': [('volume_alert_enabled', '=', False)]}"/>
                            </group>
                        </group>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>
        
        <!-- Watchlist Search View -->
        <record id="view_tradingview_watchlist_search" model="ir.ui.view">
            <field name="name">tradingview.watchlist.search</field>
            <field name="model">tradingview.watchlist</field>
            <field name="arch" type="xml">
                <search string="Search Watchlists">
                    <field name="symbol_id" string="Symbol"/>
                    <field name="user_id" string="User"/>
                    <field name="category"/>
                    <field name="custom_category"/>
                    
                    <filter string="My Watchlist" name="my_watchlist" domain="[('user_id', '=', uid)]"/>
                    <filter string="Active" name="active" domain="[('is_active', '=', True)]"/>
                    <filter string="With Alerts" name="with_alerts" domain="[('price_alert_enabled', '=', True)]"/>
                    
                    <separator/>
                    <filter string="Positive Performance" name="positive_performance" domain="[('performance_since_added', '>', 0)]"/>
                    <filter string="Negative Performance" name="negative_performance" domain="[('performance_since_added', '&lt;', 0)]"/>
                    <filter string="Target Reached" name="target_reached" domain="[('current_price', '>=', 'target_price')]"/>
                    
                    <separator/>
                    <filter string="Stocks" name="stocks" domain="[('category', '=', 'stocks')]"/>
                    <filter string="Crypto" name="crypto" domain="[('category', '=', 'crypto')]"/>
                    <filter string="Forex" name="forex" domain="[('category', '=', 'forex')]"/>
                    <filter string="Commodities" name="commodities" domain="[('category', '=', 'commodities')]"/>
                    <filter string="Favorites" name="favorites" domain="[('category', '=', 'favorites')]"/>
                    
                    <separator/>
                    <filter string="Added Today" name="added_today" 
                            domain="[('added_date', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))),
                                     ('added_date', '&lt;', datetime.datetime.combine(context_today() + datetime.timedelta(days=1), datetime.time(0,0,0)))]"/>
                    <filter string="Added This Week" name="added_this_week" 
                            domain="[('added_date', '>=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                    
                    <group expand="0" string="Group By">
                        <filter string="User" name="group_user" context="{'group_by': 'user_id'}"/>
                        <filter string="Category" name="group_category" context="{'group_by': 'category'}"/>
                        <filter string="Symbol" name="group_symbol" context="{'group_by': 'symbol_id'}"/>
                        <filter string="Added Date" name="group_added_date" context="{'group_by': 'added_date:day'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- Watchlist Kanban View -->
        <record id="view_tradingview_watchlist_kanban" model="ir.ui.view">
            <field name="name">tradingview.watchlist.kanban</field>
            <field name="model">tradingview.watchlist</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile" default_group_by="category">
                    <field name="symbol_id"/>
                    <field name="current_price"/>
                    <field name="daily_change"/>
                    <field name="daily_change_percent"/>
                    <field name="performance_since_added"/>
                    <field name="target_price"/>
                    <field name="price_alert_enabled"/>
                    <field name="category"/>
                    <field name="added_date"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="symbol_id"/>
                                            <t t-if="record.price_alert_enabled.raw_value">
                                                <i class="fa fa-bell text-warning ml-2" title="Price Alert Enabled"/>
                                            </t>
                                        </strong>
                                        <div class="o_kanban_record_subtitle">
                                            Added: <field name="added_date"/>
                                        </div>
                                    </div>
                                    <div class="o_kanban_record_body">
                                        <div class="row">
                                            <div class="col-6">
                                                <strong>Price: <field name="current_price"/></strong>
                                            </div>
                                            <div class="col-6 text-right">
                                                <span t-attf-class="badge badge-pill #{record.daily_change.raw_value >= 0 ? 'badge-success' : 'badge-danger'}">
                                                    <field name="daily_change_percent"/>%
                                                </span>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-12">
                                                <span t-attf-class="badge badge-pill #{record.performance_since_added.raw_value >= 0 ? 'badge-success' : 'badge-danger'}">
                                                    Performance: <field name="performance_since_added"/>%
                                                </span>
                                            </div>
                                        </div>
                                        <div class="row mt-2" t-if="record.target_price.raw_value">
                                            <div class="col-12">
                                                <small class="text-muted">
                                                    Target: <field name="target_price"/>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>
        
        <!-- Watchlist Dashboard View -->
        <record id="view_tradingview_watchlist_dashboard" model="ir.ui.view">
            <field name="name">tradingview.watchlist.dashboard</field>
            <field name="model">tradingview.watchlist</field>
            <field name="arch" type="xml">
                <graph string="Watchlist Performance" type="bar">
                    <field name="symbol_id" type="row"/>
                    <field name="performance_since_added" type="measure"/>
                    <field name="daily_change_percent" type="measure"/>
                </graph>
            </field>
        </record>
        
        <!-- Action -->
        <record id="action_tradingview_watchlist" model="ir.actions.act_window">
            <field name="name">Watchlists</field>
            <field name="res_model">tradingview.watchlist</field>
            <field name="view_mode">tree,kanban,form,graph</field>
            <field name="context">{'search_default_active': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No watchlist items found!
                </p>
                <p>
                    Users can add symbols to their watchlists to track performance and set price alerts.
                </p>
            </field>
        </record>
        
        <!-- My Watchlist Action -->
        <record id="action_tradingview_my_watchlist" model="ir.actions.act_window">
            <field name="name">My Watchlist</field>
            <field name="res_model">tradingview.watchlist</field>
            <field name="view_mode">kanban,tree,form,graph</field>
            <field name="domain">[('user_id', '=', uid)]</field>
            <field name="context">{'search_default_active': 1, 'default_user_id': uid}</field>
        </record>
        
    </data>
</odoo>
