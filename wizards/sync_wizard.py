# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
from datetime import timedelta
import logging

_logger = logging.getLogger(__name__)


class TradingViewSyncWizard(models.TransientModel):
    _name = 'tradingview.sync.wizard'
    _description = 'Manual Data Synchronization Wizard'

    # Sync Options
    sync_symbols = fields.Boolean(string='Sync Symbols', default=True)
    sync_ohlc = fields.Boolean(string='Sync OHLC Data', default=True)
    sync_news = fields.Boolean(string='Sync News', default=True)
    sync_events = fields.Boolean(string='Sync Events', default=False)
    sync_technical = fields.Boolean(string='Calculate Technical Indicators', default=False)
    
    # Symbol Types
    symbol_types = fields.Selection([
        ('all', 'All Types'),
        ('stock_only', 'Stocks Only'),
        ('crypto_only', 'Cryptocurrency Only'),
        ('forex_only', 'Forex Only'),
        ('custom', 'Custom Selection'),
    ], string='Symbol Types', default='all')
    
    # Custom Symbol Selection
    symbol_ids = fields.Many2many(
        'tradingview.symbol',
        string='Specific Symbols',
        help='Select specific symbols to sync (when Custom Selection is chosen)'
    )
    
    # API Sources
    use_twelvedata = fields.Boolean(string='TwelveData API', default=True)
    use_binance = fields.Boolean(string='Binance API', default=True)
    use_newsapi = fields.Boolean(string='NewsAPI', default=True)
    
    # Time Range
    days_back = fields.Integer(string='Days Back', default=7, help='Number of days to sync historical data')
    limit_records = fields.Integer(string='Limit Records', default=100, help='Maximum records per symbol')
    
    # Options
    force_update = fields.Boolean(string='Force Update', default=False, help='Update existing records')
    test_mode = fields.Boolean(string='Test Mode', default=False, help='Run in test mode (limited data)')
    
    # Results
    sync_log_ids = fields.Many2many('tradingview.sync_log', string='Sync Logs', readonly=True)
    
    @api.onchange('symbol_types')
    def _onchange_symbol_types(self):
        if self.symbol_types != 'custom':
            self.symbol_ids = [(5, 0, 0)]  # Clear selection
    
    def action_start_sync(self):
        """Start the synchronization process"""
        self.ensure_one()
        
        if not any([self.sync_symbols, self.sync_ohlc, self.sync_news, self.sync_events, self.sync_technical]):
            raise UserError(_('Please select at least one sync option.'))
        
        api_service = self.env['tradingview.api.service']
        sync_logs = []
        
        try:
            # Get symbol list based on selection
            symbols = self._get_symbol_list()
            
            # Sync Symbols
            if self.sync_symbols:
                sync_logs.extend(self._sync_symbols(api_service))
            
            # Sync OHLC Data
            if self.sync_ohlc:
                sync_logs.extend(self._sync_ohlc_data(api_service, symbols))
            
            # Sync News
            if self.sync_news:
                sync_logs.extend(self._sync_news(api_service, symbols))
            
            # Sync Events
            if self.sync_events:
                sync_logs.extend(self._sync_events(symbols))
            
            # Calculate Technical Indicators
            if self.sync_technical:
                sync_logs.extend(self._calculate_technical_indicators(symbols))
            
            # Update wizard with results
            self.sync_log_ids = [(6, 0, [log.id for log in sync_logs])]
            
            return self._show_results()
            
        except Exception as e:
            _logger.error(f"Sync wizard failed: {e}")
            raise UserError(_('Synchronization failed: %s') % str(e))
    
    def _get_symbol_list(self):
        """Get list of symbols to sync based on selection"""
        if self.symbol_types == 'custom':
            return [s.symbol for s in self.symbol_ids]
        elif self.symbol_types == 'all':
            symbols = self.env['tradingview.symbol'].search([('active', '=', True)])
        else:
            # Map wizard selection to symbol type
            type_mapping = {
                'stock_only': 'stock',
                'crypto_only': 'crypto',
                'forex_only': 'forex'
            }
            symbol_type = type_mapping.get(self.symbol_types, 'stock')
            symbols = self.env['tradingview.symbol'].search([
                ('active', '=', True),
                ('type', '=', symbol_type)
            ])
        
        # Limit for test mode
        if self.test_mode:
            symbols = symbols[:10]
        
        return [s.symbol for s in symbols]
    
    def _sync_symbols(self, api_service):
        """Sync symbols from external APIs"""
        sync_logs = []
        
        if self.use_twelvedata:
            try:
                if self.symbol_types == 'all':
                    types = ['stock', 'forex', 'crypto']
                elif self.symbol_types == 'stock_only':
                    types = ['stock']
                elif self.symbol_types == 'crypto_only':
                    types = ['crypto']
                elif self.symbol_types == 'forex_only':
                    types = ['forex']
                else:
                    types = ['stock']  # Default
                
                sync_log = api_service.sync_symbols_twelvedata(types)
                sync_logs.append(sync_log)
                
            except Exception as e:
                _logger.error(f"TwelveData symbols sync failed: {e}")
        
        return sync_logs
    
    def _sync_ohlc_data(self, api_service, symbols):
        """Sync OHLC data for symbols"""
        sync_logs = []
        
        # Binance for crypto
        if self.use_binance:
            try:
                crypto_symbols = [s for s in symbols if self._is_crypto_symbol(s)]
                if crypto_symbols:
                    sync_log = api_service.sync_crypto_data_binance(crypto_symbols[:20])
                    sync_logs.append(sync_log)
            except Exception as e:
                _logger.error(f"Binance OHLC sync failed: {e}")
        
        # TwelveData for stocks/forex
        if self.use_twelvedata:
            try:
                stock_symbols = [s for s in symbols if not self._is_crypto_symbol(s)]
                if stock_symbols:
                    sync_log = self._sync_stock_ohlc_twelvedata(api_service, stock_symbols[:20])
                    sync_logs.append(sync_log)
            except Exception as e:
                _logger.error(f"TwelveData OHLC sync failed: {e}")
        
        return sync_logs
    
    def _sync_news(self, api_service, symbols):
        """Sync news for symbols"""
        sync_logs = []
        
        if self.use_newsapi:
            try:
                # Limit symbols for news sync to avoid rate limits
                news_symbols = symbols[:10] if symbols else []
                sync_log = api_service.sync_financial_news(news_symbols, self.days_back)
                sync_logs.append(sync_log)
            except Exception as e:
                _logger.error(f"NewsAPI sync failed: {e}")
        
        return sync_logs
    
    def _sync_events(self, symbols):
        """Sync financial events"""
        sync_logs = []
        
        try:
            sync_log = self.env['tradingview.sync_log'].create_sync_log(
                'financial_modeling_prep', 'events', triggered_by='manual'
            )
            
            # Sync earnings events
            earnings_count = self._sync_earnings_events(symbols)
            
            # Sync economic events
            economic_count = self._sync_economic_events()
            
            sync_log.update_progress(
                processed=earnings_count + economic_count,
                created=earnings_count + economic_count
            )
            sync_log.mark_completed('success')
            sync_logs.append(sync_log)
            
        except Exception as e:
            sync_log.mark_failed(str(e))
            _logger.error(f"Events sync failed: {e}")
        
        return sync_logs
    
    def _calculate_technical_indicators(self, symbols):
        """Calculate technical indicators for symbols"""
        sync_logs = []
        
        try:
            sync_log = self.env['tradingview.sync_log'].create_sync_log(
                'custom', 'technical', triggered_by='manual'
            )
            
            calculated_count = 0
            for symbol in symbols[:20]:  # Limit for performance
                symbol_obj = self.env['tradingview.symbol'].search([('symbol', '=', symbol)], limit=1)
                if symbol_obj and symbol_obj.ohlc_count > 20:
                    self._calculate_symbol_indicators(symbol_obj)
                    calculated_count += 1
            
            sync_log.update_progress(
                processed=calculated_count,
                created=calculated_count * 5  # Assume 5 indicators per symbol
            )
            sync_log.mark_completed('success')
            sync_logs.append(sync_log)
            
        except Exception as e:
            sync_log.mark_failed(str(e))
            _logger.error(f"Technical indicators calculation failed: {e}")
        
        return sync_logs
    
    def _is_crypto_symbol(self, symbol):
        """Check if symbol is cryptocurrency"""
        crypto_patterns = ['BTC', 'ETH', 'ADA', 'DOT', 'LINK', 'UNI', 'USDT', 'USDC']
        return any(pattern in symbol for pattern in crypto_patterns)
    
    def _sync_stock_ohlc_twelvedata(self, api_service, symbols):
        """Sync stock OHLC data from TwelveData"""
        sync_log = self.env['tradingview.sync_log'].create_sync_log(
            'twelvedata', 'ohlc', triggered_by='manual'
        )
        
        # This would be implemented similar to Binance sync
        # For now, just mark as completed
        sync_log.mark_completed('success')
        return sync_log
    
    def _sync_earnings_events(self, symbols):
        """Sync earnings events for symbols"""
        # Mock implementation - would integrate with real API
        count = 0
        for symbol in symbols[:10]:
            symbol_obj = self.env['tradingview.symbol'].search([('symbol', '=', symbol)], limit=1)
            if symbol_obj and symbol_obj.type == 'stock':
                # Create mock earnings event
                self.env['tradingview.event'].create({
                    'symbol_id': symbol_obj.id,
                    'title': f'{symbol} Quarterly Earnings',
                    'event_type': 'earnings',
                    'date': fields.Datetime.now() + timedelta(days=30),
                    'impact_level': 'high',
                    'source': 'manual_sync'
                })
                count += 1
        return count
    
    def _sync_economic_events(self):
        """Sync economic events"""
        # Mock implementation
        return 0
    
    def _calculate_symbol_indicators(self, symbol):
        """Calculate technical indicators for a symbol"""
        # This would implement actual technical indicator calculations
        # For now, create mock indicators
        indicators = ['rsi', 'sma_20', 'sma_50', 'macd_line', 'bb_upper']
        
        for indicator in indicators:
            self.env['tradingview.technical'].create({
                'symbol_id': symbol.id,
                'indicator': indicator,
                'value': 50.0,  # Mock value
                'timestamp': fields.Datetime.now(),
                'timeframe': '1d',
                'signal': 'neutral',
                'signal_strength': 50.0
            })
    
    def _show_results(self):
        """Show synchronization results"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Synchronization Results'),
            'res_model': 'tradingview.sync.wizard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            'context': {'show_results': True}
        }
    
    def action_view_sync_logs(self):
        """View detailed sync logs"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Sync Logs'),
            'res_model': 'tradingview.sync_log',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', self.sync_log_ids.ids)],
            'target': 'current'
        }
    
    def action_close(self):
        """Close the wizard"""
        return {'type': 'ir.actions.act_window_close'}
